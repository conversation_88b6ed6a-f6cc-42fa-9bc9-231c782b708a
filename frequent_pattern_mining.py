"""
Frequent Pattern Mining Module

This module implements algorithms for mining frequent patterns from transaction data.
It includes implementations of the Apriori and FP-Growth algorithms.
"""

import pandas as pd
import numpy as np
from collections import defaultdict, Counter
from itertools import combinations
import time
from typing import List, Dict, Set, Tuple, Any, Optional, Union


class FrequentPatternMiner:
    """Base class for frequent pattern mining algorithms."""
    
    def __init__(self, min_support: float = 0.01, min_confidence: float = 0.5):
        """
        Initialize the FrequentPatternMiner.
        
        Args:
            min_support: Minimum support threshold (default: 0.01)
            min_confidence: Minimum confidence threshold for association rules (default: 0.5)
        """
        self.min_support = min_support
        self.min_confidence = min_confidence
        self.frequent_itemsets = {}
        self.association_rules = []
        self.transaction_count = 0
        
    def fit(self, transactions: List[List[str]]) -> None:
        """
        Find frequent itemsets in the given transactions.
        
        Args:
            transactions: List of transactions, where each transaction is a list of items
        """
        self.transaction_count = len(transactions)
        raise NotImplementedError("Subclasses must implement this method")
    
    def generate_association_rules(self) -> List[Dict[str, Any]]:
        """
        Generate association rules from frequent itemsets.
        
        Returns:
            List of association rules, where each rule is a dictionary with keys:
            - antecedent: Items on the left side of the rule
            - consequent: Items on the right side of the rule
            - support: Support of the rule
            - confidence: Confidence of the rule
            - lift: Lift of the rule
        """
        rules = []
        
        for itemset, support in self.frequent_itemsets.items():
            if len(itemset) < 2:
                continue
                
            for i in range(1, len(itemset)):
                for antecedent in combinations(itemset, i):
                    antecedent = tuple(sorted(antecedent))
                    consequent = tuple(sorted(item for item in itemset if item not in antecedent))
                    
                    if antecedent in self.frequent_itemsets:
                        antecedent_support = self.frequent_itemsets[antecedent]
                        confidence = support / antecedent_support
                        
                        if confidence >= self.min_confidence:
                            if consequent in self.frequent_itemsets:
                                consequent_support = self.frequent_itemsets[consequent]
                                lift = confidence / consequent_support
                            else:
                                lift = float('inf')  # This shouldn't happen with proper implementation
                            
                            rules.append({
                                'antecedent': antecedent,
                                'consequent': consequent,
                                'support': support,
                                'confidence': confidence,
                                'lift': lift
                            })
        
        # Sort rules by confidence (descending)
        self.association_rules = sorted(rules, key=lambda x: x['confidence'], reverse=True)
        return self.association_rules


class Apriori(FrequentPatternMiner):
    """Implementation of the Apriori algorithm for frequent pattern mining."""
    
    def fit(self, transactions: List[List[str]]) -> Dict[Tuple[str, ...], float]:
        """
        Find frequent itemsets using the Apriori algorithm.
        
        Args:
            transactions: List of transactions, where each transaction is a list of items
            
        Returns:
            Dictionary mapping frequent itemsets to their support
        """
        self.transaction_count = len(transactions)
        
        # Count support for individual items (1-itemsets)
        item_counts = Counter()
        for transaction in transactions:
            for item in transaction:
                item_counts[item] += 1
        
        # Filter items by minimum support
        min_count = self.min_support * self.transaction_count
        frequent_1_itemsets = {(item,): count / self.transaction_count 
                              for item, count in item_counts.items() 
                              if count >= min_count}
        
        self.frequent_itemsets = frequent_1_itemsets
        k = 2
        
        # Iteratively find frequent k-itemsets
        while True:
            # Generate candidate k-itemsets from frequent (k-1)-itemsets
            candidates = self._generate_candidates(list(self.frequent_itemsets.keys()), k)
            
            # Count support for candidate itemsets
            itemset_counts = Counter()
            for transaction in transactions:
                # Only consider candidates that could be contained in this transaction
                transaction_set = set(transaction)
                for candidate in candidates:
                    if all(item in transaction_set for item in candidate):
                        itemset_counts[candidate] += 1
            
            # Filter candidates by minimum support
            frequent_k_itemsets = {itemset: count / self.transaction_count 
                                  for itemset, count in itemset_counts.items() 
                                  if count >= min_count}
            
            # If no frequent k-itemsets found, break
            if not frequent_k_itemsets:
                break
            
            # Update frequent itemsets
            self.frequent_itemsets.update(frequent_k_itemsets)
            k += 1
        
        return self.frequent_itemsets
    
    def _generate_candidates(self, prev_frequent_itemsets: List[Tuple[str, ...]], k: int) -> List[Tuple[str, ...]]:
        """
        Generate candidate k-itemsets from frequent (k-1)-itemsets.
        
        Args:
            prev_frequent_itemsets: List of frequent (k-1)-itemsets
            k: Size of itemsets to generate
            
        Returns:
            List of candidate k-itemsets
        """
        candidates = []
        
        # Only proceed if we have enough frequent itemsets from the previous round
        if k > 1 and len(prev_frequent_itemsets) > 0:
            # Only consider (k-1)-itemsets for joining
            prev_frequent_itemsets = [itemset for itemset in prev_frequent_itemsets if len(itemset) == k-1]
            
            for i in range(len(prev_frequent_itemsets)):
                for j in range(i+1, len(prev_frequent_itemsets)):
                    itemset1 = prev_frequent_itemsets[i]
                    itemset2 = prev_frequent_itemsets[j]
                    
                    # Check if first k-2 items are the same
                    if itemset1[:-1] == itemset2[:-1]:
                        # Create a new candidate by joining the two itemsets
                        new_candidate = itemset1 + (itemset2[-1],)
                        
                        # Prune: check if all (k-1)-subsets of the candidate are frequent
                        should_add = True
                        for idx in range(k):
                            subset = new_candidate[:idx] + new_candidate[idx+1:]
                            if subset not in prev_frequent_itemsets:
                                should_add = False
                                break
                        
                        if should_add:
                            candidates.append(new_candidate)
        
        return candidates


class FPGrowth(FrequentPatternMiner):
    """Implementation of the FP-Growth algorithm for frequent pattern mining."""
    
    class FPNode:
        """Node in an FP-tree."""
        
        def __init__(self, item=None, count=0, parent=None):
            self.item = item
            self.count = count
            self.parent = parent
            self.children = {}
            self.next = None
        
        def increment(self, count=1):
            """Increment the count of this node."""
            self.count += count
    
    def fit(self, transactions: List[List[str]]) -> Dict[Tuple[str, ...], float]:
        """
        Find frequent itemsets using the FP-Growth algorithm.
        
        Args:
            transactions: List of transactions, where each transaction is a list of items
            
        Returns:
            Dictionary mapping frequent itemsets to their support
        """
        self.transaction_count = len(transactions)
        min_count = self.min_support * self.transaction_count
        
        # Count item frequencies
        item_counts = Counter()
        for transaction in transactions:
            for item in transaction:
                item_counts[item] += 1
        
        # Filter items by minimum support
        frequent_items = {item: count for item, count in item_counts.items() if count >= min_count}
        
        # If no frequent items, return empty result
        if not frequent_items:
            return {}
        
        # Sort frequent items by frequency (descending)
        item_order = {item: i for i, (item, _) in enumerate(sorted(frequent_items.items(), 
                                                                  key=lambda x: (-x[1], x[0])))}
        
        # Build FP-tree
        root = self.FPNode()
        header_table = {item: None for item in frequent_items}
        
        # Insert transactions into the tree
        for transaction in transactions:
            # Filter and sort items in the transaction
            filtered_items = [(item, item_order[item]) for item in transaction if item in frequent_items]
            filtered_items.sort(key=lambda x: x[1])
            
            if filtered_items:
                self._insert_tree([item for item, _ in filtered_items], root, header_table)
        
        # Mine frequent patterns
        self.frequent_itemsets = {}
        self._mine_tree(root, header_table, set(), self.frequent_itemsets)
        
        # Convert support counts to support values
        for itemset in self.frequent_itemsets:
            self.frequent_itemsets[itemset] /= self.transaction_count
        
        return self.frequent_itemsets
    
    def _insert_tree(self, items: List[str], node: FPNode, header_table: Dict[str, FPNode]) -> None:
        """
        Insert a transaction into the FP-tree.
        
        Args:
            items: Items in the transaction
            node: Current node in the tree
            header_table: Header table for the tree
        """
        if not items:
            return
        
        item = items[0]
        
        # If the item is already a child of the current node, increment its count
        if item in node.children:
            node.children[item].increment()
        else:
            # Create a new node for the item
            new_node = self.FPNode(item, 1, node)
            node.children[item] = new_node
            
            # Update the header table
            if header_table[item] is None:
                header_table[item] = new_node
            else:
                current = header_table[item]
                while current.next is not None:
                    current = current.next
                current.next = new_node
        
        # Recursively insert the rest of the items
        self._insert_tree(items[1:], node.children[item], header_table)
    
    def _mine_tree(self, node: FPNode, header_table: Dict[str, FPNode], 
                  prefix: Set[str], frequent_itemsets: Dict[Tuple[str, ...], int]) -> None:
        """
        Mine frequent patterns from the FP-tree.
        
        Args:
            node: Current node in the tree
            header_table: Header table for the tree
            prefix: Current prefix itemset
            frequent_itemsets: Dictionary to store frequent itemsets
        """
        # Process each item in the header table (in reverse order)
        for item, start_node in sorted(header_table.items(), reverse=True):
            # Skip if no nodes for this item
            if start_node is None:
                continue
            
            # Create a new prefix by adding the current item
            new_prefix = prefix.union({item})
            
            # Add the new prefix to the frequent itemsets
            frequent_itemsets[tuple(sorted(new_prefix))] = self._count_support(start_node)
            
            # Build conditional pattern base
            conditional_pattern_base = []
            
            # Traverse the linked list of nodes for this item
            current = start_node
            while current is not None:
                # Get the path from the root to this node
                path = []
                support = current.count
                
                # Traverse up the tree to get the path
                parent = current.parent
                while parent is not None and parent.item is not None:
                    path.append(parent.item)
                    parent = parent.parent
                
                if path:
                    # Add the path to the conditional pattern base
                    conditional_pattern_base.append((path, support))
                
                current = current.next
            
            # If the conditional pattern base is empty, continue
            if not conditional_pattern_base:
                continue
            
            # Build conditional FP-tree
            conditional_tree_root = self.FPNode()
            conditional_header_table = {}
            
            # Count item frequencies in the conditional pattern base
            conditional_item_counts = Counter()
            for path, support in conditional_pattern_base:
                for path_item in path:
                    conditional_item_counts[path_item] += support
            
            # Filter items by minimum support
            min_count = self.min_support * self.transaction_count
            conditional_frequent_items = {item: count for item, count in conditional_item_counts.items() 
                                         if count >= min_count}
            
            # If no frequent items, continue
            if not conditional_frequent_items:
                continue
            
            # Initialize conditional header table
            conditional_header_table = {item: None for item in conditional_frequent_items}
            
            # Insert paths into the conditional tree
            for path, support in conditional_pattern_base:
                # Filter and sort items in the path
                filtered_path = [item for item in path if item in conditional_frequent_items]
                
                if filtered_path:
                    # Insert the path into the tree
                    self._insert_conditional_tree(filtered_path, conditional_tree_root, 
                                                conditional_header_table, support)
            
            # Recursively mine the conditional tree
            self._mine_tree(conditional_tree_root, conditional_header_table, new_prefix, frequent_itemsets)
    
    def _insert_conditional_tree(self, items: List[str], node: FPNode, 
                               header_table: Dict[str, FPNode], count: int) -> None:
        """
        Insert a path into a conditional FP-tree.
        
        Args:
            items: Items in the path
            node: Current node in the tree
            header_table: Header table for the tree
            count: Support count for the path
        """
        if not items:
            return
        
        item = items[0]
        
        # If the item is already a child of the current node, increment its count
        if item in node.children:
            node.children[item].increment(count)
        else:
            # Create a new node for the item
            new_node = self.FPNode(item, count, node)
            node.children[item] = new_node
            
            # Update the header table
            if header_table[item] is None:
                header_table[item] = new_node
            else:
                current = header_table[item]
                while current.next is not None:
                    current = current.next
                current.next = new_node
        
        # Recursively insert the rest of the items
        self._insert_conditional_tree(items[1:], node.children[item], header_table, count)
    
    def _count_support(self, start_node: FPNode) -> int:
        """
        Count the total support for an item in the FP-tree.
        
        Args:
            start_node: First node for the item in the header table
            
        Returns:
            Total support count
        """
        count = 0
        current = start_node
        
        while current is not None:
            count += current.count
            current = current.next
        
        return count
