"""
Main Script for Frequent Pattern Mining and Recommendation System

This script demonstrates the use of the implemented modules for:
1. Loading and preprocessing transaction data
2. Mining frequent patterns using Apriori and FP-Growth algorithms
3. Building recommendation systems based on association rules and collaborative filtering
4. Evaluating and comparing the recommendation systems
5. Visualizing the results
"""

import pandas as pd
import numpy as np
import time
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Dict, Tuple, Set, Any, Optional, Union

from data_processor import (
    load_data, preprocess_transactions, create_transaction_matrix,
    get_item_stats, get_member_stats
)
from frequent_pattern_mining import Apriori, FPGrowth
from recommender import AssociationRuleRecommender, CollaborativeFilteringRecommender, HybridRecommender
from evaluation import (
    evaluate_pattern_mining, evaluate_recommender,
    evaluate_basket_completion, compare_recommenders
)
from visualization import (
    plot_item_frequency, plot_itemset_sizes, plot_rule_metrics,
    plot_rule_network, plot_recommender_comparison, plot_basket_completion,
    plot_execution_time_comparison
)


def main():
    """Main function to run the entire system."""
    print("=" * 80)
    print("频繁模式挖掘与推荐系统")
    print("=" * 80)

    # Load data
    print("\n1. 加载数据...")
    train_data = load_data("Groceries data train.csv")
    test_data = load_data("Groceries data test.csv")

    # Preprocess data
    print("\n2. 预处理数据...")
    train_transactions = preprocess_transactions(train_data)
    test_transactions = preprocess_transactions(test_data)

    # Create transaction matrix for collaborative filtering
    transaction_matrix = create_transaction_matrix(train_data)

    # Get item and member statistics
    print("\n3. 分析数据...")
    item_stats = get_item_stats(train_data)
    member_stats = get_member_stats(train_data)

    print(f"前10个最常购买的商品:")
    print(item_stats.head(10))

    print(f"\n会员统计信息:")
    print(f"每位会员平均购买商品数: {member_stats['item_count'].mean():.2f}")
    print(f"每位会员平均购买不同商品数: {member_stats['unique_item_count'].mean():.2f}")

    # Mine frequent patterns
    print("\n4. 挖掘频繁模式...")

    # Set parameters
    min_support = 0.01  # 1% 最小支持度
    min_confidence = 0.5  # 50% 最小置信度

    # Initialize algorithms
    apriori = Apriori(min_support=min_support, min_confidence=min_confidence)
    fp_growth = FPGrowth(min_support=min_support, min_confidence=min_confidence)

    # Evaluate Apriori
    print("\n评估Apriori算法...")
    apriori_results = evaluate_pattern_mining(apriori, train_transactions)

    print(f"执行时间: {apriori_results['execution_time']:.2f} 秒")
    print(f"频繁项集数量: {apriori_results['num_frequent_itemsets']}")
    print(f"关联规则数量: {apriori_results['num_rules']}")
    print(f"平均规则置信度: {apriori_results['avg_confidence']:.4f}")
    print(f"平均规则提升度: {apriori_results['avg_lift']:.4f}")

    # Evaluate FP-Growth
    print("\n评估FP-Growth算法...")
    fp_growth_results = evaluate_pattern_mining(fp_growth, train_transactions)

    print(f"执行时间: {fp_growth_results['execution_time']:.2f} 秒")
    print(f"频繁项集数量: {fp_growth_results['num_frequent_itemsets']}")
    print(f"关联规则数量: {fp_growth_results['num_rules']}")
    print(f"平均规则置信度: {fp_growth_results['avg_confidence']:.4f}")
    print(f"平均规则提升度: {fp_growth_results['avg_lift']:.4f}")

    # Compare algorithms
    print("\n算法比较:")
    print(f"Apriori执行时间: {apriori_results['execution_time']:.2f} 秒")
    print(f"FP-Growth执行时间: {fp_growth_results['execution_time']:.2f} 秒")
    print(f"速度提升: {apriori_results['execution_time'] / fp_growth_results['execution_time']:.2f}倍")

    # Visualize results
    print("\n可视化频繁模式挖掘结果...")
    plot_item_frequency(item_stats)
    plot_itemset_sizes(fp_growth_results['itemset_sizes'])
    plot_rule_metrics(fp_growth.association_rules)
    plot_rule_network(fp_growth.association_rules)
    plot_execution_time_comparison(apriori_results['execution_time'], fp_growth_results['execution_time'])

    # Build recommendation systems
    print("\n5. 构建推荐系统...")

    # Use the faster algorithm for recommendations
    pattern_miner = fp_growth if fp_growth_results['execution_time'] < apriori_results['execution_time'] else apriori

    # Initialize recommenders
    rule_recommender = AssociationRuleRecommender(min_confidence=min_confidence)
    cf_recommender = CollaborativeFilteringRecommender(similarity_threshold=0.2)
    hybrid_recommender = HybridRecommender(rule_weight=0.7)

    # Fit recommenders
    print("训练关联规则推荐器...")
    rule_recommender.fit(pattern_miner)

    print("训练协同过滤推荐器...")
    cf_recommender.fit(transaction_matrix)

    print("训练混合推荐器...")
    hybrid_recommender.fit(pattern_miner, transaction_matrix)

    # Evaluate recommenders
    print("\n6. 评估推荐系统...")

    recommenders = {
        '关联规则': rule_recommender,
        '协同过滤': cf_recommender,
        '混合推荐': hybrid_recommender
    }

    # Compare recommenders
    comparison_results = compare_recommenders(recommenders, test_transactions, k=5)
    print("\n推荐系统比较:")
    print(comparison_results)

    # Visualize recommender comparison
    print("\n可视化推荐系统比较...")
    plot_recommender_comparison(comparison_results)

    # Evaluate basket completion
    print("\n7. 评估购物篮补全...")

    basket_sizes = [1, 2, 3]
    basket_results = {}

    for name, recommender in recommenders.items():
        print(f"\n评估{name}推荐器的购物篮补全性能:")
        results = evaluate_basket_completion(recommender, test_transactions, basket_sizes, k=5)
        basket_results[name] = results

        for basket_size, metrics in results.items():
            print(f"  {basket_size.replace('_', ' ')}: 准确率={metrics['precision@k']:.4f}, "
                  f"召回率={metrics['recall@k']:.4f}, F1值={metrics['f1@k']:.4f}")

    # Visualize basket completion results
    print("\n可视化购物篮补全结果...")
    plot_basket_completion(basket_results)

    # Example recommendations
    print("\n8. 示例推荐:")

    # Select a random test transaction
    np.random.seed(42)
    example_idx = np.random.randint(0, len(test_transactions))
    example_transaction = test_transactions[example_idx]

    print(f"示例购物篮: {example_transaction}")

    # Generate recommendations using each recommender
    for name, recommender in recommenders.items():
        print(f"\n{name}推荐结果:")
        recommendations = recommender.recommend(example_transaction, top_n=5)

        for i, rec in enumerate(recommendations, 1):
            if name == '关联规则':
                print(f"  {i}. {rec['item']} (置信度: {rec['confidence']:.4f}, 提升度: {rec['lift']:.4f})")
            elif name == '协同过滤':
                print(f"  {i}. {rec['item']} (相似度: {rec['similarity']:.4f})")
            else:  # Hybrid
                print(f"  {i}. {rec['item']} (得分: {rec['score']:.4f})")

    print("\n" + "=" * 80)
    print("分析完成!")
    print("=" * 80)


if __name__ == "__main__":
    main()
