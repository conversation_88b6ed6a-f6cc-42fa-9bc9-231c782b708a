"""
Visualization Module

This module provides functions for visualizing the results of frequent pattern mining
and recommendation systems.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import networkx as nx
from typing import List, Dict, Tuple, Set, Any, Optional, Union
from collections import defaultdict


def plot_item_frequency(item_stats: pd.DataFrame, top_n: int = 20, figsize: Tuple[int, int] = (12, 8)) -> None:
    """
    Plot the frequency of the top N items.

    Args:
        item_stats: DataFrame with item statistics
        top_n: Number of top items to plot
        figsize: Figure size (width, height)
    """
    plt.figure(figsize=figsize)

    # Get top N items
    top_items = item_stats.head(top_n)

    # Create bar plot
    sns.barplot(x='count', y='item', data=top_items)

    plt.title(f'前 {top_n} 个最常购买的商品')
    plt.xlabel('数量')
    plt.ylabel('商品')
    plt.tight_layout()
    plt.savefig('item_frequency.png')
    plt.close()

    print(f"商品频率图已保存至 'item_frequency.png'")


def plot_itemset_sizes(itemset_sizes: Dict[int, int], figsize: Tuple[int, int] = (10, 6)) -> None:
    """
    Plot the distribution of frequent itemset sizes.

    Args:
        itemset_sizes: Dictionary mapping itemset sizes to counts
        figsize: Figure size (width, height)
    """
    plt.figure(figsize=figsize)

    # Convert to DataFrame
    df = pd.DataFrame({
        'size': list(itemset_sizes.keys()),
        'count': list(itemset_sizes.values())
    })

    # Sort by size
    df = df.sort_values('size')

    # Create bar plot
    sns.barplot(x='size', y='count', data=df)

    plt.title('频繁项集大小分布')
    plt.xlabel('项集大小')
    plt.ylabel('数量')
    plt.xticks(range(len(df)), df['size'])
    plt.tight_layout()
    plt.savefig('itemset_sizes.png')
    plt.close()

    print(f"项集大小分布图已保存至 'itemset_sizes.png'")


def plot_rule_metrics(rules: List[Dict[str, Any]], figsize: Tuple[int, int] = (12, 8)) -> None:
    """
    Plot the distribution of rule metrics (confidence and lift).

    Args:
        rules: List of association rules
        figsize: Figure size (width, height)
    """
    plt.figure(figsize=figsize)

    # Convert to DataFrame
    df = pd.DataFrame({
        'confidence': [rule['confidence'] for rule in rules],
        'lift': [min(rule['lift'], 10) for rule in rules]  # Cap lift at 10 for better visualization
    })

    # Create scatter plot
    plt.subplot(1, 2, 1)
    sns.histplot(df['confidence'], bins=20, kde=True)
    plt.title('规则置信度分布')
    plt.xlabel('置信度')
    plt.ylabel('数量')

    plt.subplot(1, 2, 2)
    sns.histplot(df['lift'], bins=20, kde=True)
    plt.title('规则提升度分布')
    plt.xlabel('提升度 (上限为10)')
    plt.ylabel('数量')

    plt.tight_layout()
    plt.savefig('rule_metrics.png')
    plt.close()

    print(f"规则指标图已保存至 'rule_metrics.png'")


def plot_rule_network(rules: List[Dict[str, Any]], min_confidence: float = 0.7,
                     max_rules: int = 50, figsize: Tuple[int, int] = (12, 10)) -> None:
    """
    Plot a network of association rules.

    Args:
        rules: List of association rules
        min_confidence: Minimum confidence for rules to include
        max_rules: Maximum number of rules to include
        figsize: Figure size (width, height)
    """
    # Filter rules by confidence
    filtered_rules = [rule for rule in rules if rule['confidence'] >= min_confidence]

    # Sort by confidence (descending)
    filtered_rules.sort(key=lambda x: x['confidence'], reverse=True)

    # Limit number of rules
    filtered_rules = filtered_rules[:max_rules]

    # Create graph
    G = nx.DiGraph()

    # Add edges for each rule
    for rule in filtered_rules:
        for item_from in rule['antecedent']:
            for item_to in rule['consequent']:
                if G.has_edge(item_from, item_to):
                    # Increase edge weight
                    G[item_from][item_to]['weight'] += 1
                    G[item_from][item_to]['confidence'] = max(G[item_from][item_to]['confidence'], rule['confidence'])
                else:
                    # Add new edge
                    G.add_edge(item_from, item_to, weight=1, confidence=rule['confidence'])

    # Create plot
    fig, ax = plt.subplots(figsize=figsize)

    # Set node positions
    pos = nx.spring_layout(G, seed=42)

    # Get edge weights and confidences
    edge_weights = [G[u][v]['weight'] for u, v in G.edges()]
    edge_confidences = [G[u][v]['confidence'] for u, v in G.edges()]

    # Normalize edge widths
    max_weight = max(edge_weights) if edge_weights else 1
    edge_widths = [1 + 3 * (w / max_weight) for w in edge_weights]

    # Draw nodes
    nx.draw_networkx_nodes(G, pos, node_size=500, node_color='lightblue', alpha=0.8, ax=ax)

    # Draw edges
    edges = nx.draw_networkx_edges(G, pos, width=edge_widths, edge_color=edge_confidences,
                                 edge_cmap=plt.cm.Blues, arrows=True, arrowsize=15,
                                 connectionstyle='arc3,rad=0.1', ax=ax)

    # Add colorbar
    if edge_confidences:
        sm = plt.cm.ScalarMappable(cmap=plt.cm.Blues, norm=plt.Normalize(vmin=min_confidence, vmax=1))
        sm.set_array([])
        cbar = fig.colorbar(sm, ax=ax)
        cbar.set_label('置信度')

    # Draw labels
    nx.draw_networkx_labels(G, pos, font_size=10, font_family='sans-serif', ax=ax)

    ax.set_title(f'关联规则网络 (前 {len(filtered_rules)} 条规则)')
    ax.axis('off')
    plt.tight_layout()
    plt.savefig('rule_network.png')
    plt.close()

    print(f"Saved rule network plot to 'rule_network.png'")


def plot_recommender_comparison(comparison_results: pd.DataFrame, figsize: Tuple[int, int] = (12, 8)) -> None:
    """
    Plot a comparison of recommendation systems.

    Args:
        comparison_results: DataFrame with comparison results
        figsize: Figure size (width, height)
    """
    plt.figure(figsize=figsize)

    # Melt DataFrame for easier plotting
    melted = pd.melt(comparison_results,
                    id_vars=['recommender'],
                    value_vars=['precision@k', 'recall@k', 'f1@k', 'hit_rate@k'],
                    var_name='metric', value_name='value')

    # Create grouped bar plot
    sns.barplot(x='recommender', y='value', hue='metric', data=melted)

    plt.title('推荐系统比较')
    plt.xlabel('推荐系统')
    plt.ylabel('指标值')
    plt.legend(title='指标')
    plt.tight_layout()
    plt.savefig('recommender_comparison.png')
    plt.close()

    print(f"推荐系统比较图已保存至 'recommender_comparison.png'")


def plot_basket_completion(basket_results: Dict[str, Dict[str, Dict[str, float]]],
                         figsize: Tuple[int, int] = (15, 10)) -> None:
    """
    Plot basket completion results for different recommenders and basket sizes.

    Args:
        basket_results: Dictionary mapping recommender names to basket completion results
        figsize: Figure size (width, height)
    """
    plt.figure(figsize=figsize)

    # Convert to DataFrame
    rows = []
    for recommender, results in basket_results.items():
        for basket_size, metrics in results.items():
            size = int(basket_size.split('_')[-1])
            rows.append({
                'recommender': recommender,
                'basket_size': size,
                'precision': metrics['precision@k'],
                'recall': metrics['recall@k'],
                'f1': metrics['f1@k']
            })

    df = pd.DataFrame(rows)

    # Create subplots
    metrics = ['precision', 'recall', 'f1']
    for i, metric in enumerate(metrics, 1):
        plt.subplot(1, 3, i)

        # Create line plot
        sns.lineplot(x='basket_size', y=metric, hue='recommender', data=df, marker='o')

        metric_names = {'precision': '准确率', 'recall': '召回率', 'f1': 'F1值'}
        plt.title(f'不同购物篮大小的{metric_names.get(metric, metric)}')
        plt.xlabel('购物篮大小')
        plt.ylabel(metric_names.get(metric, metric))
        plt.xticks(df['basket_size'].unique())

    plt.tight_layout()
    plt.savefig('basket_completion.png')
    plt.close()

    print(f"购物篮补全分析图已保存至 'basket_completion.png'")


def plot_execution_time_comparison(apriori_time: float, fp_growth_time: float,
                                 figsize: Tuple[int, int] = (8, 6)) -> None:
    """
    Plot a comparison of execution times for Apriori and FP-Growth algorithms.

    Args:
        apriori_time: Execution time for Apriori algorithm
        fp_growth_time: Execution time for FP-Growth algorithm
        figsize: Figure size (width, height)
    """
    plt.figure(figsize=figsize)

    # Create DataFrame
    df = pd.DataFrame({
        'algorithm': ['Apriori', 'FP-Growth'],
        'execution_time': [apriori_time, fp_growth_time]
    })

    # Create bar plot
    sns.barplot(x='algorithm', y='execution_time', data=df)

    plt.title('算法执行时间比较')
    plt.xlabel('算法')
    plt.ylabel('执行时间（秒）')
    plt.tight_layout()
    plt.savefig('execution_time.png')
    plt.close()

    print(f"算法执行时间比较图已保存至 'execution_time.png'")
