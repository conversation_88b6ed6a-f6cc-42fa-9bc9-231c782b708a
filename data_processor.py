"""
Data Processing Module

This module provides functions for loading and preprocessing transaction data
for frequent pattern mining and recommendation systems.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Set, Any, Optional, Union
from collections import defaultdict


def load_data(file_path: str) -> pd.DataFrame:
    """
    Load transaction data from a CSV file.

    Args:
        file_path: Path to the CSV file

    Returns:
        DataFrame containing the transaction data
    """
    try:
        df = pd.read_csv(file_path)
        print(f"成功加载数据: {file_path}")
        print(f"数据形状: {df.shape}")
        return df
    except Exception as e:
        print(f"加载数据出错 {file_path}: {e}")
        raise


def preprocess_transactions(df: pd.DataFrame,
                           member_col: str = 'Member_number',
                           item_col: str = 'itemDescription') -> List[List[str]]:
    """
    Preprocess transaction data for frequent pattern mining.

    Args:
        df: DataFrame containing transaction data
        member_col: Name of the column containing member/transaction IDs
        item_col: Name of the column containing item descriptions

    Returns:
        List of transactions, where each transaction is a list of items
    """
    # Group items by member/transaction ID
    grouped = df.groupby(member_col)[item_col].apply(list).reset_index()

    # Convert to list of transactions
    transactions = grouped[item_col].tolist()

    print(f"预处理完成，共 {len(transactions)} 条交易记录")
    return transactions


def create_transaction_matrix(df: pd.DataFrame,
                             member_col: str = 'Member_number',
                             item_col: str = 'itemDescription') -> pd.DataFrame:
    """
    Create a binary transaction matrix for collaborative filtering.

    Args:
        df: DataFrame containing transaction data
        member_col: Name of the column containing member/transaction IDs
        item_col: Name of the column containing item descriptions

    Returns:
        Binary transaction matrix (members x items)
    """
    # Create a pivot table with members as rows and items as columns
    transaction_matrix = pd.crosstab(df[member_col], df[item_col])

    print(f"创建交易矩阵，形状为: {transaction_matrix.shape}")
    return transaction_matrix


def split_data(df: pd.DataFrame, test_size: float = 0.2, random_state: Optional[int] = None) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Split data into training and testing sets.

    Args:
        df: DataFrame containing transaction data
        test_size: Proportion of data to use for testing
        random_state: Random seed for reproducibility

    Returns:
        Tuple of (training_data, testing_data)
    """
    # Get unique member IDs
    member_ids = df['Member_number'].unique()

    # Set random seed if provided
    if random_state is not None:
        np.random.seed(random_state)

    # Randomly select members for testing
    test_size_int = int(len(member_ids) * test_size)
    test_members = np.random.choice(member_ids, size=test_size_int, replace=False)

    # Split data
    test_data = df[df['Member_number'].isin(test_members)]
    train_data = df[~df['Member_number'].isin(test_members)]

    print(f"将数据分为训练集 ({len(train_data)} 行) 和测试集 ({len(test_data)} 行)")
    return train_data, test_data


def get_item_stats(df: pd.DataFrame, item_col: str = 'itemDescription') -> pd.DataFrame:
    """
    Get statistics for items in the dataset.

    Args:
        df: DataFrame containing transaction data
        item_col: Name of the column containing item descriptions

    Returns:
        DataFrame with item statistics
    """
    # Count occurrences of each item
    item_counts = df[item_col].value_counts().reset_index()
    item_counts.columns = ['item', 'count']

    # Calculate frequency
    item_counts['frequency'] = item_counts['count'] / len(df)

    # Sort by count (descending)
    item_counts = item_counts.sort_values('count', ascending=False)

    return item_counts


def get_member_stats(df: pd.DataFrame, member_col: str = 'Member_number', item_col: str = 'itemDescription') -> pd.DataFrame:
    """
    Get statistics for members in the dataset.

    Args:
        df: DataFrame containing transaction data
        member_col: Name of the column containing member/transaction IDs
        item_col: Name of the column containing item descriptions

    Returns:
        DataFrame with member statistics
    """
    # Count items purchased by each member
    member_counts = df.groupby(member_col)[item_col].count().reset_index()
    member_counts.columns = ['member', 'item_count']

    # Count unique items purchased by each member
    member_unique_counts = df.groupby(member_col)[item_col].nunique().reset_index()
    member_unique_counts.columns = ['member', 'unique_item_count']

    # Merge counts
    member_stats = pd.merge(member_counts, member_unique_counts, on='member')

    # Sort by item count (descending)
    member_stats = member_stats.sort_values('item_count', ascending=False)

    return member_stats
