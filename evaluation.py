"""
Evaluation Module

This module provides functions for evaluating frequent pattern mining algorithms
and recommendation systems.
"""

import pandas as pd
import numpy as np
import time
from typing import List, Dict, Tuple, Set, Any, Optional, Union
from collections import defaultdict
from frequent_pattern_mining import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from recommender import <PERSON><PERSON>uleRecom<PERSON><PERSON>, CollaborativeFilteringRecommender, HybridRecommender


def evaluate_pattern_mining(algorithm: FrequentPatternMiner, transactions: List[List[str]]) -> Dict[str, Any]:
    """
    Evaluate a frequent pattern mining algorithm.
    
    Args:
        algorithm: FrequentPatternMiner instance
        transactions: List of transactions
        
    Returns:
        Dictionary with evaluation metrics
    """
    # Measure execution time
    start_time = time.time()
    frequent_itemsets = algorithm.fit(transactions)
    end_time = time.time()
    
    # Calculate metrics
    execution_time = end_time - start_time
    num_frequent_itemsets = len(frequent_itemsets)
    
    # Count itemsets by size
    itemset_sizes = defaultdict(int)
    for itemset in frequent_itemsets:
        itemset_sizes[len(itemset)] += 1
    
    # Generate association rules
    start_time = time.time()
    rules = algorithm.generate_association_rules()
    end_time = time.time()
    
    rule_generation_time = end_time - start_time
    num_rules = len(rules)
    
    # Calculate rule metrics
    if rules:
        avg_confidence = np.mean([rule['confidence'] for rule in rules])
        avg_lift = np.mean([rule['lift'] for rule in rules])
        max_confidence = max([rule['confidence'] for rule in rules])
        max_lift = max([rule['lift'] for rule in rules])
    else:
        avg_confidence = 0
        avg_lift = 0
        max_confidence = 0
        max_lift = 0
    
    return {
        'execution_time': execution_time,
        'num_frequent_itemsets': num_frequent_itemsets,
        'itemset_sizes': dict(itemset_sizes),
        'rule_generation_time': rule_generation_time,
        'num_rules': num_rules,
        'avg_confidence': avg_confidence,
        'avg_lift': avg_lift,
        'max_confidence': max_confidence,
        'max_lift': max_lift
    }


def evaluate_recommender(recommender: Union[AssociationRuleRecommender, CollaborativeFilteringRecommender, HybridRecommender],
                        test_transactions: List[List[str]],
                        k: int = 5) -> Dict[str, float]:
    """
    Evaluate a recommendation system using leave-one-out cross-validation.
    
    Args:
        recommender: Trained recommender instance
        test_transactions: List of test transactions
        k: Number of recommendations to generate
        
    Returns:
        Dictionary with evaluation metrics
    """
    # Initialize metrics
    precision_sum = 0
    recall_sum = 0
    f1_sum = 0
    hit_rate_sum = 0
    
    # Evaluate on each test transaction
    for transaction in test_transactions:
        # Skip transactions with fewer than 2 items
        if len(transaction) < 2:
            continue
        
        # Leave one out: use all but one item for recommendation
        for i in range(len(transaction)):
            test_item = transaction[i]
            basket = transaction[:i] + transaction[i+1:]
            
            # Get recommendations
            recommendations = recommender.recommend(basket, top_n=k)
            recommended_items = [rec['item'] for rec in recommendations]
            
            # Calculate metrics
            hit = test_item in recommended_items
            
            # Precision, recall, and F1 are all the same in this case (leave-one-out)
            precision = 1.0 if hit else 0.0
            recall = 1.0 if hit else 0.0
            f1 = 1.0 if hit else 0.0
            
            precision_sum += precision
            recall_sum += recall
            f1_sum += f1
            hit_rate_sum += 1 if hit else 0
    
    # Calculate average metrics
    num_evaluations = sum(len(transaction) for transaction in test_transactions if len(transaction) >= 2)
    
    if num_evaluations > 0:
        precision = precision_sum / num_evaluations
        recall = recall_sum / num_evaluations
        f1 = f1_sum / num_evaluations
        hit_rate = hit_rate_sum / num_evaluations
    else:
        precision = 0
        recall = 0
        f1 = 0
        hit_rate = 0
    
    return {
        'precision@k': precision,
        'recall@k': recall,
        'f1@k': f1,
        'hit_rate@k': hit_rate,
        'k': k,
        'num_evaluations': num_evaluations
    }


def evaluate_basket_completion(recommender: Union[AssociationRuleRecommender, CollaborativeFilteringRecommender, HybridRecommender],
                              test_transactions: List[List[str]],
                              basket_sizes: List[int] = [1, 2, 3],
                              k: int = 5) -> Dict[str, Dict[str, float]]:
    """
    Evaluate a recommendation system for basket completion.
    
    Args:
        recommender: Trained recommender instance
        test_transactions: List of test transactions
        basket_sizes: List of basket sizes to evaluate
        k: Number of recommendations to generate
        
    Returns:
        Dictionary with evaluation metrics for each basket size
    """
    results = {}
    
    for basket_size in basket_sizes:
        # Initialize metrics
        precision_sum = 0
        recall_sum = 0
        f1_sum = 0
        num_evaluations = 0
        
        # Evaluate on each test transaction
        for transaction in test_transactions:
            # Skip transactions with too few items
            if len(transaction) <= basket_size:
                continue
            
            # Use the first 'basket_size' items as the basket
            basket = transaction[:basket_size]
            remaining_items = set(transaction[basket_size:])
            
            # Get recommendations
            recommendations = recommender.recommend(basket, top_n=k)
            recommended_items = set(rec['item'] for rec in recommendations)
            
            # Calculate metrics
            true_positives = len(recommended_items.intersection(remaining_items))
            
            precision = true_positives / len(recommended_items) if recommended_items else 0
            recall = true_positives / len(remaining_items) if remaining_items else 0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            
            precision_sum += precision
            recall_sum += recall
            f1_sum += f1
            num_evaluations += 1
        
        # Calculate average metrics
        if num_evaluations > 0:
            results[f'basket_size_{basket_size}'] = {
                'precision@k': precision_sum / num_evaluations,
                'recall@k': recall_sum / num_evaluations,
                'f1@k': f1_sum / num_evaluations,
                'num_evaluations': num_evaluations
            }
        else:
            results[f'basket_size_{basket_size}'] = {
                'precision@k': 0,
                'recall@k': 0,
                'f1@k': 0,
                'num_evaluations': 0
            }
    
    return results


def compare_recommenders(recommenders: Dict[str, Union[AssociationRuleRecommender, CollaborativeFilteringRecommender, HybridRecommender]],
                        test_transactions: List[List[str]],
                        k: int = 5) -> pd.DataFrame:
    """
    Compare multiple recommendation systems.
    
    Args:
        recommenders: Dictionary mapping recommender names to trained recommender instances
        test_transactions: List of test transactions
        k: Number of recommendations to generate
        
    Returns:
        DataFrame with comparison results
    """
    results = []
    
    for name, recommender in recommenders.items():
        # Evaluate recommender
        metrics = evaluate_recommender(recommender, test_transactions, k)
        
        # Add name to metrics
        metrics['recommender'] = name
        
        # Add to results
        results.append(metrics)
    
    # Convert to DataFrame
    results_df = pd.DataFrame(results)
    
    # Reorder columns
    column_order = ['recommender', 'precision@k', 'recall@k', 'f1@k', 'hit_rate@k', 'k', 'num_evaluations']
    results_df = results_df[column_order]
    
    return results_df
