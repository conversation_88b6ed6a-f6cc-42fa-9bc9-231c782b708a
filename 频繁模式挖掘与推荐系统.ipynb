{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 频繁模式挖掘与推荐系统\n", "\n", "本项目实现了一个完整的杂货店推荐系统，包括：\n", "1. 频繁模式挖掘（Apriori和FP-Growth算法）\n", "2. 关联规则生成\n", "3. 多种推荐系统（基于关联规则、协同过滤、混合推荐）\n", "4. 性能评估和可视化分析\n", "\n", "## 项目背景\n", "您正在一家杂货店的开发团队中工作。店长注意到有些商品经常被一起购买，希望找出顾客最常一起购买的商品集，用于商品布局优化和个性化推荐。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 导入必要的库"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 基础库\n", "import pandas as pd\n", "import numpy as np\n", "import time\n", "import warnings\n", "from collections import defaultdict, Counter\n", "from itertools import combinations\n", "from typing import List, Dict, Tuple, Set, Any, Optional, Union\n", "\n", "# 可视化库\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import networkx as nx\n", "\n", "# 机器学习库\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "\n", "# 设置中文字体和图形样式\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']  # 支持中文显示\n", "plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号\n", "sns.set_style(\"whitegrid\")\n", "warnings.filterwarnings('ignore')  # 忽略警告信息\n", "\n", "print(\"✅ 所有库导入成功！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 数据处理模块"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_data(file_path: str) -> pd.DataFrame:\n", "    \"\"\"\n", "    加载交易数据\n", "    \n", "    参数:\n", "        file_path: CSV文件路径\n", "        \n", "    返回:\n", "        包含交易数据的DataFrame\n", "    \"\"\"\n", "    try:\n", "        df = pd.read_csv(file_path)\n", "        print(f\"✅ 成功加载数据: {file_path}\")\n", "        print(f\"📊 数据形状: {df.shape}\")\n", "        return df\n", "    except Exception as e:\n", "        print(f\"❌ 加载数据出错 {file_path}: {e}\")\n", "        raise\n", "\n", "def preprocess_transactions(df: pd.DataFrame, \n", "                           member_col: str = 'Member_number', \n", "                           item_col: str = 'itemDescription') -> List[List[str]]:\n", "    \"\"\"\n", "    预处理交易数据，将其转换为频繁模式挖掘所需的格式\n", "    \n", "    参数:\n", "        df: 包含交易数据的DataFrame\n", "        member_col: 会员/交易ID列名\n", "        item_col: 商品描述列名\n", "        \n", "    返回:\n", "        交易列表，每个交易是一个商品列表\n", "    \"\"\"\n", "    # 按会员/交易ID分组，将商品聚合为列表\n", "    grouped = df.groupby(member_col)[item_col].apply(list).reset_index()\n", "    \n", "    # 转换为交易列表\n", "    transactions = grouped[item_col].tolist()\n", "    \n", "    print(f\"✅ 预处理完成，共 {len(transactions)} 条交易记录\")\n", "    return transactions\n", "\n", "def create_transaction_matrix(df: pd.DataFrame, \n", "                             member_col: str = 'Member_number', \n", "                             item_col: str = 'itemDescription') -> pd.DataFrame:\n", "    \"\"\"\n", "    创建用于协同过滤的二进制交易矩阵\n", "    \n", "    参数:\n", "        df: 包含交易数据的DataFrame\n", "        member_col: 会员/交易ID列名\n", "        item_col: 商品描述列名\n", "        \n", "    返回:\n", "        二进制交易矩阵 (会员 x 商品)\n", "    \"\"\"\n", "    # 创建透视表，会员为行，商品为列\n", "    transaction_matrix = pd.crosstab(df[member_col], df[item_col])\n", "    \n", "    print(f\"✅ 创建交易矩阵，形状为: {transaction_matrix.shape}\")\n", "    return transaction_matrix\n", "\n", "def get_item_stats(df: pd.DataFrame, item_col: str = 'itemDescription') -> pd.DataFrame:\n", "    \"\"\"\n", "    获取商品统计信息\n", "    \n", "    参数:\n", "        df: 包含交易数据的DataFrame\n", "        item_col: 商品描述列名\n", "        \n", "    返回:\n", "        包含商品统计信息的DataFrame\n", "    \"\"\"\n", "    # 统计每个商品的出现次数\n", "    item_counts = df[item_col].value_counts().reset_index()\n", "    item_counts.columns = ['item', 'count']\n", "    \n", "    # 计算频率\n", "    item_counts['frequency'] = item_counts['count'] / len(df)\n", "    \n", "    # 按出现次数降序排列\n", "    item_counts = item_counts.sort_values('count', ascending=False)\n", "    \n", "    return item_counts\n", "\n", "def get_member_stats(df: pd.DataFrame, \n", "                    member_col: str = 'Member_number', \n", "                    item_col: str = 'itemDescription') -> pd.DataFrame:\n", "    \"\"\"\n", "    获取会员统计信息\n", "    \n", "    参数:\n", "        df: 包含交易数据的DataFrame\n", "        member_col: 会员/交易ID列名\n", "        item_col: 商品描述列名\n", "        \n", "    返回:\n", "        包含会员统计信息的DataFrame\n", "    \"\"\"\n", "    # 统计每个会员购买的商品总数\n", "    member_counts = df.groupby(member_col)[item_col].count().reset_index()\n", "    member_counts.columns = ['member', 'item_count']\n", "    \n", "    # 统计每个会员购买的不同商品数\n", "    member_unique_counts = df.groupby(member_col)[item_col].nunique().reset_index()\n", "    member_unique_counts.columns = ['member', 'unique_item_count']\n", "    \n", "    # 合并统计信息\n", "    member_stats = pd.merge(member_counts, member_unique_counts, on='member')\n", "    \n", "    # 按商品总数降序排列\n", "    member_stats = member_stats.sort_values('item_count', ascending=False)\n", "    \n", "    return member_stats\n", "\n", "print(\"✅ 数据处理模块定义完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 频繁模式挖掘模块"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class FrequentPatternMiner:\n", "    \"\"\"\n", "    频繁模式挖掘算法的基类\n", "    \"\"\"\n", "    \n", "    def __init__(self, min_support: float = 0.01, min_confidence: float = 0.5):\n", "        \"\"\"\n", "        初始化频繁模式挖掘器\n", "        \n", "        参数:\n", "            min_support: 最小支持度阈值 (默认: 0.01)\n", "            min_confidence: 关联规则的最小置信度阈值 (默认: 0.5)\n", "        \"\"\"\n", "        self.min_support = min_support\n", "        self.min_confidence = min_confidence\n", "        self.frequent_itemsets = {}\n", "        self.association_rules = []\n", "        self.transaction_count = 0\n", "        \n", "    def fit(self, transactions: List[List[str]]) -> None:\n", "        \"\"\"\n", "        在给定的交易数据中寻找频繁项集\n", "        \n", "        参数:\n", "            transactions: 交易列表，每个交易是一个商品列表\n", "        \"\"\"\n", "        self.transaction_count = len(transactions)\n", "        raise NotImplementedError(\"子类必须实现此方法\")\n", "    \n", "    def generate_association_rules(self) -> List[Dict[str, Any]]:\n", "        \"\"\"\n", "        从频繁项集生成关联规则\n", "        \n", "        返回:\n", "            关联规则列表，每个规则包含:\n", "            - antecedent: 规则左侧的商品\n", "            - consequent: 规则右侧的商品\n", "            - support: 规则的支持度\n", "            - confidence: 规则的置信度\n", "            - lift: 规则的提升度\n", "        \"\"\"\n", "        rules = []\n", "        \n", "        for itemset, support in self.frequent_itemsets.items():\n", "            if len(itemset) < 2:\n", "                continue\n", "                \n", "            # 生成所有可能的规则\n", "            for i in range(1, len(itemset)):\n", "                for antecedent in combinations(itemset, i):\n", "                    antecedent = tuple(sorted(antecedent))\n", "                    consequent = tuple(sorted(item for item in itemset if item not in antecedent))\n", "                    \n", "                    if antecedent in self.frequent_itemsets:\n", "                        antecedent_support = self.frequent_itemsets[antecedent]\n", "                        confidence = support / antecedent_support\n", "                        \n", "                        if confidence >= self.min_confidence:\n", "                            if consequent in self.frequent_itemsets:\n", "                                consequent_support = self.frequent_itemsets[consequent]\n", "                                lift = confidence / consequent_support\n", "                            else:\n", "                                lift = float('inf')\n", "                            \n", "                            rules.append({\n", "                                'antecedent': antecedent,\n", "                                'consequent': consequent,\n", "                                'support': support,\n", "                                'confidence': confidence,\n", "                                'lift': lift\n", "                            })\n", "        \n", "        # 按置信度降序排列\n", "        self.association_rules = sorted(rules, key=lambda x: x['confidence'], reverse=True)\n", "        return self.association_rules\n", "\n", "print(\"✅ 频繁模式挖掘基类定义完成！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class Apriori(FrequentPatternMiner):\n", "    \"\"\"\n", "    Apriori算法实现\n", "    \n", "    基于\"频繁项集的所有子集也是频繁的\"这一性质，\n", "    通过迭代生成候选项集并筛选频繁项集。\n", "    \"\"\"\n", "    \n", "    def fit(self, transactions: List[List[str]]) -> Dict[Tuple[str, ...], float]:\n", "        \"\"\"\n", "        使用Apriori算法寻找频繁项集\n", "        \n", "        参数:\n", "            transactions: 交易列表，每个交易是一个商品列表\n", "            \n", "        返回:\n", "            频繁项集字典，映射项集到其支持度\n", "        \"\"\"\n", "        self.transaction_count = len(transactions)\n", "        \n", "        # 统计单个商品的支持度 (1-项集)\n", "        item_counts = Counter()\n", "        for transaction in transactions:\n", "            for item in transaction:\n", "                item_counts[item] += 1\n", "        \n", "        # 根据最小支持度筛选商品\n", "        min_count = self.min_support * self.transaction_count\n", "        frequent_1_itemsets = {(item,): count / self.transaction_count \n", "                              for item, count in item_counts.items() \n", "                              if count >= min_count}\n", "        \n", "        self.frequent_itemsets = frequent_1_itemsets\n", "        k = 2\n", "        \n", "        # 迭代寻找频繁k-项集\n", "        while True:\n", "            # 从频繁(k-1)-项集生成候选k-项集\n", "            candidates = self._generate_candidates(list(self.frequent_itemsets.keys()), k)\n", "            \n", "            # 统计候选项集的支持度\n", "            itemset_counts = Counter()\n", "            for transaction in transactions:\n", "                transaction_set = set(transaction)\n", "                for candidate in candidates:\n", "                    if all(item in transaction_set for item in candidate):\n", "                        itemset_counts[candidate] += 1\n", "            \n", "            # 根据最小支持度筛选候选项集\n", "            frequent_k_itemsets = {itemset: count / self.transaction_count \n", "                                  for itemset, count in itemset_counts.items() \n", "                                  if count >= min_count}\n", "            \n", "            # 如果没有找到频繁k-项集，则停止\n", "            if not frequent_k_itemsets:\n", "                break\n", "            \n", "            # 更新频繁项集\n", "            self.frequent_itemsets.update(frequent_k_itemsets)\n", "            k += 1\n", "        \n", "        return self.frequent_itemsets\n", "    \n", "    def _generate_candidates(self, prev_frequent_itemsets: List[Tuple[str, ...]], k: int) -> List[Tuple[str, ...]]:\n", "        \"\"\"\n", "        从频繁(k-1)-项集生成候选k-项集\n", "        \n", "        参数:\n", "            prev_frequent_itemsets: 频繁(k-1)-项集列表\n", "            k: 要生成的项集大小\n", "            \n", "        返回:\n", "            候选k-项集列表\n", "        \"\"\"\n", "        candidates = []\n", "        \n", "        if k > 1 and len(prev_frequent_itemsets) > 0:\n", "            # 只考虑(k-1)-项集进行连接\n", "            prev_frequent_itemsets = [itemset for itemset in prev_frequent_itemsets if len(itemset) == k-1]\n", "            \n", "            for i in range(len(prev_frequent_itemsets)):\n", "                for j in range(i+1, len(prev_frequent_itemsets)):\n", "                    itemset1 = prev_frequent_itemsets[i]\n", "                    itemset2 = prev_frequent_itemsets[j]\n", "                    \n", "                    # 检查前k-2个商品是否相同\n", "                    if itemset1[:-1] == itemset2[:-1]:\n", "                        # 通过连接两个项集创建新的候选项集\n", "                        new_candidate = itemset1 + (itemset2[-1],)\n", "                        \n", "                        # 剪枝：检查候选项集的所有(k-1)-子集是否都是频繁的\n", "                        should_add = True\n", "                        for idx in range(k):\n", "                            subset = new_candidate[:idx] + new_candidate[idx+1:]\n", "                            if subset not in prev_frequent_itemsets:\n", "                                should_add = False\n", "                                break\n", "                        \n", "                        if should_add:\n", "                            candidates.append(new_candidate)\n", "        \n", "        return candidates\n", "\n", "print(\"✅ Apriori算法定义完成！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class FPGrowth(FrequentPatternMiner):\n", "    \"\"\"\n", "    FP-Growth算法实现\n", "    \n", "    通过构建FP树来避免生成候选项集，比Apriori算法更高效。\n", "    \"\"\"\n", "    \n", "    class FPNode:\n", "        \"\"\"FP树中的节点\"\"\"\n", "        \n", "        def __init__(self, item=None, count=0, parent=None):\n", "            self.item = item\n", "            self.count = count\n", "            self.parent = parent\n", "            self.children = {}\n", "            self.next = None\n", "        \n", "        def increment(self, count=1):\n", "            \"\"\"增加此节点的计数\"\"\"\n", "            self.count += count\n", "    \n", "    def fit(self, transactions: List[List[str]]) -> Dict[Tuple[str, ...], float]:\n", "        \"\"\"\n", "        使用FP-Growth算法寻找频繁项集\n", "        \n", "        参数:\n", "            transactions: 交易列表，每个交易是一个商品列表\n", "            \n", "        返回:\n", "            频繁项集字典，映射项集到其支持度\n", "        \"\"\"\n", "        self.transaction_count = len(transactions)\n", "        min_count = self.min_support * self.transaction_count\n", "        \n", "        # 统计商品频率\n", "        item_counts = Counter()\n", "        for transaction in transactions:\n", "            for item in transaction:\n", "                item_counts[item] += 1\n", "        \n", "        # 根据最小支持度筛选商品\n", "        frequent_items = {item: count for item, count in item_counts.items() if count >= min_count}\n", "        \n", "        # 如果没有频繁商品，返回空结果\n", "        if not frequent_items:\n", "            return {}\n", "        \n", "        # 按频率降序排列频繁商品\n", "        item_order = {item: i for i, (item, _) in enumerate(sorted(frequent_items.items(), \n", "                                                                  key=lambda x: (-x[1], x[0])))}\n", "        \n", "        # 构建FP树\n", "        root = self.FPNode()\n", "        header_table = {item: None for item in frequent_items}\n", "        \n", "        # 将交易插入树中\n", "        for transaction in transactions:\n", "            # 筛选并排序交易中的商品\n", "            filtered_items = [(item, item_order[item]) for item in transaction if item in frequent_items]\n", "            filtered_items.sort(key=lambda x: x[1])\n", "            \n", "            if filtered_items:\n", "                self._insert_tree([item for item, _ in filtered_items], root, header_table)\n", "        \n", "        # 挖掘频繁模式\n", "        self.frequent_itemsets = {}\n", "        self._mine_tree(root, header_table, set(), self.frequent_itemsets)\n", "        \n", "        # 将支持度计数转换为支持度值\n", "        for itemset in self.frequent_itemsets:\n", "            self.frequent_itemsets[itemset] /= self.transaction_count\n", "        \n", "        return self.frequent_itemsets\n", "    \n", "    def _insert_tree(self, items: List[str], node, header_table: Dict[str, Any]) -> None:\n", "        \"\"\"\n", "        将一个交易插入FP树\n", "        \n", "        参数:\n", "            items: 交易中的商品\n", "            node: 当前树节点\n", "            header_table: 头表\n", "        \"\"\"\n", "        if not items:\n", "            return\n", "        \n", "        item = items[0]\n", "        \n", "        # 如果商品已经是当前节点的子节点，增加其计数\n", "        if item in node.children:\n", "            node.children[item].increment()\n", "        else:\n", "            # 为商品创建新节点\n", "            new_node = self.FPNode(item, 1, node)\n", "            node.children[item] = new_node\n", "            \n", "            # 更新头表\n", "            if header_table[item] is None:\n", "                header_table[item] = new_node\n", "            else:\n", "                current = header_table[item]\n", "                while current.next is not None:\n", "                    current = current.next\n", "                current.next = new_node\n", "        \n", "        # 递归插入剩余商品\n", "        self._insert_tree(items[1:], node.children[item], header_table)\n", "    \n", "    def _mine_tree(self, node, header_table: Dict[str, Any], \n", "                  prefix: Set[str], frequent_itemsets: Dict[Tuple[str, ...], int]) -> None:\n", "        \"\"\"\n", "        从FP树中挖掘频繁模式\n", "        \n", "        参数:\n", "            node: 当前树节点\n", "            header_table: 头表\n", "            prefix: 当前前缀项集\n", "            frequent_itemsets: 存储频繁项集的字典\n", "        \"\"\"\n", "        # 按逆序处理头表中的每个商品\n", "        for item, start_node in sorted(header_table.items(), reverse=True):\n", "            # 跳过没有节点的商品\n", "            if start_node is None:\n", "                continue\n", "            \n", "            # 通过添加当前商品创建新前缀\n", "            new_prefix = prefix.union({item})\n", "            \n", "            # 将新前缀添加到频繁项集中\n", "            frequent_itemsets[tuple(sorted(new_prefix))] = self._count_support(start_node)\n", "            \n", "            # 构建条件模式基\n", "            conditional_pattern_base = []\n", "            \n", "            # 遍历该商品的链表节点\n", "            current = start_node\n", "            while current is not None:\n", "                # 获取从根到此节点的路径\n", "                path = []\n", "                support = current.count\n", "                \n", "                # 向上遍历树获取路径\n", "                parent = current.parent\n", "                while parent is not None and parent.item is not None:\n", "                    path.append(parent.item)\n", "                    parent = parent.parent\n", "                \n", "                if path:\n", "                    # 将路径添加到条件模式基\n", "                    conditional_pattern_base.append((path, support))\n", "                \n", "                current = current.next\n", "            \n", "            # 如果条件模式基为空，继续\n", "            if not conditional_pattern_base:\n", "                continue\n", "            \n", "            # 构建条件FP树\n", "            conditional_tree_root = self.FPNode()\n", "            conditional_header_table = {}\n", "            \n", "            # 统计条件模式基中商品的频率\n", "            conditional_item_counts = Counter()\n", "            for path, support in conditional_pattern_base:\n", "                for path_item in path:\n", "                    conditional_item_counts[path_item] += support\n", "            \n", "            # 根据最小支持度筛选商品\n", "            min_count = self.min_support * self.transaction_count\n", "            conditional_frequent_items = {item: count for item, count in conditional_item_counts.items() \n", "                                         if count >= min_count}\n", "            \n", "            # 如果没有频繁商品，继续\n", "            if not conditional_frequent_items:\n", "                continue\n", "            \n", "            # 初始化条件头表\n", "            conditional_header_table = {item: None for item in conditional_frequent_items}\n", "            \n", "            # 将路径插入条件树\n", "            for path, support in conditional_pattern_base:\n", "                # 筛选并排序路径中的商品\n", "                filtered_path = [item for item in path if item in conditional_frequent_items]\n", "                \n", "                if filtered_path:\n", "                    # 将路径插入树中\n", "                    self._insert_conditional_tree(filtered_path, conditional_tree_root, \n", "                                                conditional_header_table, support)\n", "            \n", "            # 递归挖掘条件树\n", "            self._mine_tree(conditional_tree_root, conditional_header_table, new_prefix, frequent_itemsets)\n", "    \n", "    def _insert_conditional_tree(self, items: List[str], node, \n", "                               header_table: Dict[str, Any], count: int) -> None:\n", "        \"\"\"\n", "        将路径插入条件FP树\n", "        \n", "        参数:\n", "            items: 路径中的商品\n", "            node: 当前树节点\n", "            header_table: 头表\n", "            count: 路径的支持度计数\n", "        \"\"\"\n", "        if not items:\n", "            return\n", "        \n", "        item = items[0]\n", "        \n", "        # 如果商品已经是当前节点的子节点，增加其计数\n", "        if item in node.children:\n", "            node.children[item].increment(count)\n", "        else:\n", "            # 为商品创建新节点\n", "            new_node = self.FPNode(item, count, node)\n", "            node.children[item] = new_node\n", "            \n", "            # 更新头表\n", "            if header_table[item] is None:\n", "                header_table[item] = new_node\n", "            else:\n", "                current = header_table[item]\n", "                while current.next is not None:\n", "                    current = current.next\n", "                current.next = new_node\n", "        \n", "        # 递归插入剩余商品\n", "        self._insert_conditional_tree(items[1:], node.children[item], header_table, count)\n", "    \n", "    def _count_support(self, start_node) -> int:\n", "        \"\"\"\n", "        统计FP树中某个商品的总支持度\n", "        \n", "        参数:\n", "            start_node: 头表中该商品的第一个节点\n", "            \n", "        返回:\n", "            总支持度计数\n", "        \"\"\"\n", "        count = 0\n", "        current = start_node\n", "        \n", "        while current is not None:\n", "            count += current.count\n", "            current = current.next\n", "        \n", "        return count\n", "\n", "print(\"✅ FP-Growth算法定义完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 推荐系统模块"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class AssociationRuleRecommender:\n", "    \"\"\"\n", "    基于关联规则的推荐系统\n", "    \n", "    利用挖掘出的关联规则进行商品推荐。\n", "    \"\"\"\n", "    \n", "    def __init__(self, min_confidence: float = 0.5):\n", "        \"\"\"\n", "        初始化关联规则推荐器\n", "        \n", "        参数:\n", "            min_confidence: 推荐的最小置信度阈值\n", "        \"\"\"\n", "        self.min_confidence = min_confidence\n", "        self.rules = []\n", "        self.item_to_rules = defaultdict(list)\n", "    \n", "    def fit(self, pattern_miner: FrequentPatternMiner) -> None:\n", "        \"\"\"\n", "        使用模式挖掘器的关联规则训练推荐器\n", "        \n", "        参数:\n", "            pattern_miner: 已训练的频繁模式挖掘器实例\n", "        \"\"\"\n", "        # 从模式挖掘器获取关联规则\n", "        self.rules = pattern_miner.generate_association_rules()\n", "        \n", "        # 创建从商品到规则的映射，以便快速查找\n", "        for i, rule in enumerate(self.rules):\n", "            for item in rule['antecedent']:\n", "                self.item_to_rules[item].append(i)\n", "    \n", "    def recommend(self, items: List[str], top_n: int = 5) -> List[Dict[str, Any]]:\n", "        \"\"\"\n", "        基于用户购物篮推荐商品\n", "        \n", "        参数:\n", "            items: 用户购物篮中的商品列表\n", "            top_n: 返回的推荐数量\n", "            \n", "        返回:\n", "            推荐列表，每个推荐包含:\n", "            - item: 推荐的商品\n", "            - confidence: 推荐的置信度\n", "            - support: 推荐的支持度\n", "            - lift: 推荐的提升度\n", "        \"\"\"\n", "        # 寻找匹配用户购物篮的规则\n", "        matching_rules = []\n", "        items_set = set(items)\n", "        \n", "        # 收集前件是用户购物篮子集的规则\n", "        for item in items:\n", "            for rule_idx in self.item_to_rules[item]:\n", "                rule = self.rules[rule_idx]\n", "                if set(rule['antecedent']).issubset(items_set):\n", "                    matching_rules.append(rule)\n", "        \n", "        # 如果没有匹配的规则，返回空列表\n", "        if not matching_rules:\n", "            return []\n", "        \n", "        # 按商品聚合推荐\n", "        item_scores = defaultdict(lambda: {'confidence': 0, 'support': 0, 'lift': 0, 'count': 0})\n", "        \n", "        for rule in matching_rules:\n", "            for item in rule['consequent']:\n", "                if item not in items_set:  # 只推荐购物篮中没有的商品\n", "                    item_scores[item]['confidence'] += rule['confidence']\n", "                    item_scores[item]['support'] += rule['support']\n", "                    item_scores[item]['lift'] += rule['lift']\n", "                    item_scores[item]['count'] += 1\n", "        \n", "        # 计算平均分数\n", "        recommendations = []\n", "        for item, scores in item_scores.items():\n", "            count = scores['count']\n", "            if count > 0:\n", "                recommendations.append({\n", "                    'item': item,\n", "                    'confidence': scores['confidence'] / count,\n", "                    'support': scores['support'] / count,\n", "                    'lift': scores['lift'] / count\n", "                })\n", "        \n", "        # 按置信度降序排列并返回前N个\n", "        recommendations.sort(key=lambda x: x['confidence'], reverse=True)\n", "        return recommendations[:top_n]\n", "\n", "print(\"✅ 关联规则推荐器定义完成！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class CollaborativeFilteringRecommender:\n", "    \"\"\"\n", "    基于协同过滤的推荐系统\n", "    \n", "    通过计算商品之间的相似度进行推荐。\n", "    \"\"\"\n", "    \n", "    def __init__(self, similarity_threshold: float = 0.2):\n", "        \"\"\"\n", "        初始化协同过滤推荐器\n", "        \n", "        参数:\n", "            similarity_threshold: 推荐的最小相似度阈值\n", "        \"\"\"\n", "        self.similarity_threshold = similarity_threshold\n", "        self.transaction_matrix = None\n", "        self.item_similarity = None\n", "        self.items = None\n", "    \n", "    def fit(self, transaction_matrix: pd.DataFrame) -> None:\n", "        \"\"\"\n", "        使用交易矩阵训练推荐器\n", "        \n", "        参数:\n", "            transaction_matrix: 二进制交易矩阵 (会员 x 商品)\n", "        \"\"\"\n", "        self.transaction_matrix = transaction_matrix\n", "        self.items = transaction_matrix.columns.tolist()\n", "        \n", "        # 计算商品-商品相似度矩阵\n", "        self.item_similarity = pd.DataFrame(\n", "            cosine_similarity(transaction_matrix.T),\n", "            index=self.items,\n", "            columns=self.items\n", "        )\n", "        \n", "        # 将对角线设为0（商品与自身的相似度）\n", "        np.fill_diagonal(self.item_similarity.values, 0)\n", "    \n", "    def recommend(self, items: List[str], top_n: int = 5) -> List[Dict[str, Any]]:\n", "        \"\"\"\n", "        基于用户购物篮推荐商品\n", "        \n", "        参数:\n", "            items: 用户购物篮中的商品列表\n", "            top_n: 返回的推荐数量\n", "            \n", "        返回:\n", "            推荐列表，每个推荐包含:\n", "            - item: 推荐的商品\n", "            - similarity: 相似度分数\n", "        \"\"\"\n", "        # 检查商品是否存在于训练数据中\n", "        valid_items = [item for item in items if item in self.items]\n", "        \n", "        # 如果没有有效商品，返回空列表\n", "        if not valid_items:\n", "            return []\n", "        \n", "        # 计算所有商品的相似度分数\n", "        similarity_scores = defaultdict(float)\n", "        \n", "        for user_item in valid_items:\n", "            for candidate_item in self.items:\n", "                if candidate_item not in items:  # 只推荐购物篮中没有的商品\n", "                    similarity = self.item_similarity.loc[user_item, candidate_item]\n", "                    if similarity >= self.similarity_threshold:\n", "                        similarity_scores[candidate_item] += similarity\n", "        \n", "        # 转换为推荐列表\n", "        recommendations = [\n", "            {'item': item, 'similarity': score}\n", "            for item, score in similarity_scores.items()\n", "        ]\n", "        \n", "        # 按相似度降序排列并返回前N个\n", "        recommendations.sort(key=lambda x: x['similarity'], reverse=True)\n", "        return recommendations[:top_n]\n", "\n", "print(\"✅ 协同过滤推荐器定义完成！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class HybridRecommender:\n", "    \"\"\"\n", "    混合推荐系统\n", "    \n", "    结合关联规则和协同过滤的推荐结果。\n", "    \"\"\"\n", "    \n", "    def __init__(self, rule_weight: float = 0.7):\n", "        \"\"\"\n", "        初始化混合推荐器\n", "        \n", "        参数:\n", "            rule_weight: 关联规则推荐的权重 (0-1)\n", "        \"\"\"\n", "        self.rule_weight = rule_weight\n", "        self.rule_recommender = AssociationRuleRecommender()\n", "        self.cf_recommender = CollaborativeFilteringRecommender()\n", "    \n", "    def fit(self, pattern_miner: <PERSON><PERSON>quentPatternMiner, transaction_matrix: pd.DataFrame) -> None:\n", "        \"\"\"\n", "        训练混合推荐器\n", "        \n", "        参数:\n", "            pattern_miner: 已训练的频繁模式挖掘器实例\n", "            transaction_matrix: 二进制交易矩阵 (会员 x 商品)\n", "        \"\"\"\n", "        self.rule_recommender.fit(pattern_miner)\n", "        self.cf_recommender.fit(transaction_matrix)\n", "    \n", "    def recommend(self, items: List[str], top_n: int = 5) -> List[Dict[str, Any]]:\n", "        \"\"\"\n", "        基于用户购物篮推荐商品\n", "        \n", "        参数:\n", "            items: 用户购物篮中的商品列表\n", "            top_n: 返回的推荐数量\n", "            \n", "        返回:\n", "            推荐列表，每个推荐包含:\n", "            - item: 推荐的商品\n", "            - score: 综合推荐分数\n", "        \"\"\"\n", "        # 从两个推荐器获取推荐\n", "        rule_recs = self.rule_recommender.recommend(items, top_n=top_n*2)\n", "        cf_recs = self.cf_recommender.recommend(items, top_n=top_n*2)\n", "        \n", "        # 标准化分数\n", "        if rule_recs:\n", "            max_confidence = max(rec['confidence'] for rec in rule_recs)\n", "            for rec in rule_recs:\n", "                rec['normalized_score'] = rec['confidence'] / max_confidence if max_confidence > 0 else 0\n", "        \n", "        if cf_recs:\n", "            max_similarity = max(rec['similarity'] for rec in cf_recs)\n", "            for rec in cf_recs:\n", "                rec['normalized_score'] = rec['similarity'] / max_similarity if max_similarity > 0 else 0\n", "        \n", "        # 合并推荐\n", "        item_scores = {}\n", "        \n", "        for rec in rule_recs:\n", "            item_scores[rec['item']] = self.rule_weight * rec['normalized_score']\n", "        \n", "        for rec in cf_recs:\n", "            if rec['item'] in item_scores:\n", "                item_scores[rec['item']] += (1 - self.rule_weight) * rec['normalized_score']\n", "            else:\n", "                item_scores[rec['item']] = (1 - self.rule_weight) * rec['normalized_score']\n", "        \n", "        # 转换为推荐列表\n", "        recommendations = [\n", "            {'item': item, 'score': score}\n", "            for item, score in item_scores.items()\n", "        ]\n", "        \n", "        # 按分数降序排列并返回前N个\n", "        recommendations.sort(key=lambda x: x['score'], reverse=True)\n", "        return recommendations[:top_n]\n", "\n", "print(\"✅ 混合推荐器定义完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 评估模块"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def evaluate_pattern_mining(algorithm: FrequentPatternMiner, transactions: List[List[str]]) -> Dict[str, Any]:\n", "    \"\"\"\n", "    评估频繁模式挖掘算法的性能\n", "    \n", "    参数:\n", "        algorithm: 频繁模式挖掘器实例\n", "        transactions: 交易列表\n", "        \n", "    返回:\n", "        包含评估指标的字典\n", "    \"\"\"\n", "    # 测量执行时间\n", "    start_time = time.time()\n", "    frequent_itemsets = algorithm.fit(transactions)\n", "    end_time = time.time()\n", "    \n", "    # 计算指标\n", "    execution_time = end_time - start_time\n", "    num_frequent_itemsets = len(frequent_itemsets)\n", "    \n", "    # 按项集大小统计\n", "    itemset_sizes = defaultdict(int)\n", "    for itemset in frequent_itemsets:\n", "        itemset_sizes[len(itemset)] += 1\n", "    \n", "    # 生成关联规则\n", "    start_time = time.time()\n", "    rules = algorithm.generate_association_rules()\n", "    end_time = time.time()\n", "    \n", "    rule_generation_time = end_time - start_time\n", "    num_rules = len(rules)\n", "    \n", "    # 计算规则指标\n", "    if rules:\n", "        avg_confidence = np.mean([rule['confidence'] for rule in rules])\n", "        valid_lifts = [rule['lift'] for rule in rules if rule['lift'] != float('inf')]\n", "        avg_lift = np.mean(valid_lifts) if valid_lifts else 0\n", "        max_confidence = max([rule['confidence'] for rule in rules])\n", "        max_lift = max(valid_lifts) if valid_lifts else 0\n", "    else:\n", "        avg_confidence = 0\n", "        avg_lift = 0\n", "        max_confidence = 0\n", "        max_lift = 0\n", "    \n", "    return {\n", "        'execution_time': execution_time,\n", "        'num_frequent_itemsets': num_frequent_itemsets,\n", "        'itemset_sizes': dict(itemset_sizes),\n", "        'rule_generation_time': rule_generation_time,\n", "        'num_rules': num_rules,\n", "        'avg_confidence': avg_confidence,\n", "        'avg_lift': avg_lift,\n", "        'max_confidence': max_confidence,\n", "        'max_lift': max_lift\n", "    }\n", "\n", "def evaluate_recommender(recommender, test_transactions: List[List[str]], k: int = 5) -> Dict[str, float]:\n", "    \"\"\"\n", "    使用留一法交叉验证评估推荐系统\n", "    \n", "    参数:\n", "        recommender: 已训练的推荐器实例\n", "        test_transactions: 测试交易列表\n", "        k: 推荐数量\n", "        \n", "    返回:\n", "        包含评估指标的字典\n", "    \"\"\"\n", "    # 初始化指标\n", "    precision_sum = 0\n", "    recall_sum = 0\n", "    f1_sum = 0\n", "    hit_rate_sum = 0\n", "    \n", "    # 在每个测试交易上进行评估\n", "    for transaction in test_transactions:\n", "        # 跳过少于2个商品的交易\n", "        if len(transaction) < 2:\n", "            continue\n", "        \n", "        # 留一法：使用除一个商品外的所有商品进行推荐\n", "        for i in range(len(transaction)):\n", "            test_item = transaction[i]\n", "            basket = transaction[:i] + transaction[i+1:]\n", "            \n", "            # 获取推荐\n", "            recommendations = recommender.recommend(basket, top_n=k)\n", "            recommended_items = [rec['item'] for rec in recommendations]\n", "            \n", "            # 计算指标\n", "            hit = test_item in recommended_items\n", "            \n", "            # 在留一法中，准确率、召回率和F1值都相同\n", "            precision = 1.0 if hit else 0.0\n", "            recall = 1.0 if hit else 0.0\n", "            f1 = 1.0 if hit else 0.0\n", "            \n", "            precision_sum += precision\n", "            recall_sum += recall\n", "            f1_sum += f1\n", "            hit_rate_sum += 1 if hit else 0\n", "    \n", "    # 计算平均指标\n", "    num_evaluations = sum(len(transaction) for transaction in test_transactions if len(transaction) >= 2)\n", "    \n", "    if num_evaluations > 0:\n", "        precision = precision_sum / num_evaluations\n", "        recall = recall_sum / num_evaluations\n", "        f1 = f1_sum / num_evaluations\n", "        hit_rate = hit_rate_sum / num_evaluations\n", "    else:\n", "        precision = 0\n", "        recall = 0\n", "        f1 = 0\n", "        hit_rate = 0\n", "    \n", "    return {\n", "        'precision@k': precision,\n", "        'recall@k': recall,\n", "        'f1@k': f1,\n", "        'hit_rate@k': hit_rate,\n", "        'k': k,\n", "        'num_evaluations': num_evaluations\n", "    }\n", "\n", "def compare_recommenders(recommenders: Dict[str, Any], test_transactions: List[List[str]], k: int = 5) -> pd.DataFrame:\n", "    \"\"\"\n", "    比较多个推荐系统的性能\n", "    \n", "    参数:\n", "        recommenders: 推荐器名称到推荐器实例的映射\n", "        test_transactions: 测试交易列表\n", "        k: 推荐数量\n", "        \n", "    返回:\n", "        包含比较结果的DataFrame\n", "    \"\"\"\n", "    results = []\n", "    \n", "    for name, recommender in recommenders.items():\n", "        # 评估推荐器\n", "        metrics = evaluate_recommender(recommender, test_transactions, k)\n", "        \n", "        # 添加名称到指标中\n", "        metrics['recommender'] = name\n", "        \n", "        # 添加到结果中\n", "        results.append(metrics)\n", "    \n", "    # 转换为DataFrame\n", "    results_df = pd.DataFrame(results)\n", "    \n", "    # 重新排列列\n", "    column_order = ['recommender', 'precision@k', 'recall@k', 'f1@k', 'hit_rate@k', 'k', 'num_evaluations']\n", "    results_df = results_df[column_order]\n", "    \n", "    return results_df\n", "\n", "print(\"✅ 评估模块定义完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 可视化模块"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def plot_item_frequency(item_stats: pd.DataFrame, top_n: int = 20, figsize: Tuple[int, int] = (12, 8)) -> None:\n", "    \"\"\"\n", "    绘制前N个最常购买商品的频率图\n", "    \n", "    参数:\n", "        item_stats: 包含商品统计信息的DataFrame\n", "        top_n: 显示前N个商品\n", "        figsize: 图形大小 (宽, 高)\n", "    \"\"\"\n", "    plt.figure(figsize=figsize)\n", "    \n", "    # 获取前N个商品\n", "    top_items = item_stats.head(top_n)\n", "    \n", "    # 创建条形图\n", "    sns.barplot(x='count', y='item', data=top_items)\n", "    \n", "    plt.title(f'前 {top_n} 个最常购买的商品')\n", "    plt.xlabel('购买次数')\n", "    plt.ylabel('商品')\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(f\"✅ 商品频率图已显示\")\n", "\n", "def plot_itemset_sizes(itemset_sizes: Dict[int, int], figsize: Tuple[int, int] = (10, 6)) -> None:\n", "    \"\"\"\n", "    绘制频繁项集大小分布图\n", "    \n", "    参数:\n", "        itemset_sizes: 项集大小到数量的映射\n", "        figsize: 图形大小 (宽, 高)\n", "    \"\"\"\n", "    plt.figure(figsize=figsize)\n", "    \n", "    # 转换为DataFrame\n", "    df = pd.DataFrame({\n", "        'size': list(itemset_sizes.keys()),\n", "        'count': list(itemset_sizes.values())\n", "    })\n", "    \n", "    # 按大小排序\n", "    df = df.sort_values('size')\n", "    \n", "    # 创建条形图\n", "    sns.barplot(x='size', y='count', data=df)\n", "    \n", "    plt.title('频繁项集大小分布')\n", "    plt.xlabel('项集大小')\n", "    plt.ylabel('数量')\n", "    plt.xticks(range(len(df)), df['size'])\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(f\"✅ 项集大小分布图已显示\")\n", "\n", "def plot_rule_metrics(rules: List[Dict[str, Any]], figsize: Tuple[int, int] = (12, 8)) -> None:\n", "    \"\"\"\n", "    绘制关联规则指标分布图\n", "    \n", "    参数:\n", "        rules: 关联规则列表\n", "        figsize: 图形大小 (宽, 高)\n", "    \"\"\"\n", "    if not rules:\n", "        print(\"⚠️ 没有关联规则可以绘制\")\n", "        return\n", "    \n", "    plt.figure(figsize=figsize)\n", "    \n", "    # 分别处理置信度和提升度数据，避免长度不一致问题\n", "    confidence_values = [rule['confidence'] for rule in rules]\n", "    lift_values = [min(rule['lift'], 10) for rule in rules if rule['lift'] != float('inf')]\n", "    \n", "    # 创建子图\n", "    plt.subplot(1, 2, 1)\n", "    if confidence_values:\n", "        sns.histplot(confidence_values, bins=20, kde=True)\n", "        plt.title('规则置信度分布')\n", "        plt.xlabel('置信度')\n", "        plt.ylabel('数量')\n", "    else:\n", "        plt.text(0.5, 0.5, '无置信度数据', ha='center', va='center', transform=plt.gca().transAxes)\n", "        plt.title('规则置信度分布')\n", "    \n", "    plt.subplot(1, 2, 2)\n", "    if lift_values:\n", "        sns.histplot(lift_values, bins=20, kde=True)\n", "        plt.title('规则提升度分布')\n", "        plt.xlabel('提升度 (上限为10)')\n", "        plt.ylabel('数量')\n", "    else:\n", "        plt.text(0.5, 0.5, '无有效提升度数据', ha='center', va='center', transform=plt.gca().transAxes)\n", "        plt.title('规则提升度分布')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(f\"✅ 规则指标图已显示 (置信度: {len(confidence_values)} 条, 提升度: {len(lift_values)} 条)\")\n", "\n", "def plot_recommender_comparison(comparison_results: pd.DataFrame, figsize: Tuple[int, int] = (12, 8)) -> None:\n", "    \"\"\"\n", "    绘制推荐系统比较图\n", "    \n", "    参数:\n", "        comparison_results: 包含比较结果的DataFrame\n", "        figsize: 图形大小 (宽, 高)\n", "    \"\"\"\n", "    plt.figure(figsize=figsize)\n", "    \n", "    # 重塑DataFrame以便绘图\n", "    melted = pd.melt(comparison_results, \n", "                    id_vars=['recommender'], \n", "                    value_vars=['precision@k', 'recall@k', 'f1@k', 'hit_rate@k'],\n", "                    var_name='metric', value_name='value')\n", "    \n", "    # 创建分组条形图\n", "    sns.barplot(x='recommender', y='value', hue='metric', data=melted)\n", "    \n", "    plt.title('推荐系统性能比较')\n", "    plt.xlabel('推荐系统')\n", "    plt.ylabel('指标值')\n", "    plt.legend(title='指标')\n", "    plt.xticks(rotation=45)\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(f\"✅ 推荐系统比较图已显示\")\n", "\n", "def plot_execution_time_comparison(apriori_time: float, fp_growth_time: float, \n", "                                 figsize: Tuple[int, int] = (8, 6)) -> None:\n", "    \"\"\"\n", "    绘制算法执行时间比较图\n", "    \n", "    参数:\n", "        apriori_time: <PERSON><PERSON>i算法执行时间\n", "        fp_growth_time: FP-Growth算法执行时间\n", "        figsize: 图形大小 (宽, 高)\n", "    \"\"\"\n", "    plt.figure(figsize=figsize)\n", "    \n", "    # 创建DataFrame\n", "    df = pd.DataFrame({\n", "        'algorithm': ['Apriori', 'FP-Growth'],\n", "        'execution_time': [apriori_time, fp_growth_time]\n", "    })\n", "    \n", "    # 创建条形图\n", "    sns.barplot(x='algorithm', y='execution_time', data=df)\n", "    \n", "    plt.title('算法执行时间比较')\n", "    plt.xlabel('算法')\n", "    plt.ylabel('执行时间（秒）')\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(f\"✅ 算法执行时间比较图已显示\")\n", "\n", "print(\"✅ 可视化模块定义完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 数据加载与预处理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 加载数据\n", "print(\"=\" * 80)\n", "print(\"🛒 频繁模式挖掘与推荐系统\")\n", "print(\"=\" * 80)\n", "\n", "print(\"\\n📂 1. 加载数据...\")\n", "train_data = load_data(\"Groceries data train.csv\")\n", "test_data = load_data(\"Groceries data test.csv\")\n", "\n", "# 显示数据基本信息\n", "print(f\"\\n📊 训练数据基本信息:\")\n", "print(f\"   - 数据形状: {train_data.shape}\")\n", "print(f\"   - 列名: {list(train_data.columns)}\")\n", "print(f\"\\n前5行数据:\")\n", "print(train_data.head())\n", "\n", "print(f\"\\n📊 测试数据基本信息:\")\n", "print(f\"   - 数据形状: {test_data.shape}\")\n", "print(f\"   - 列名: {list(test_data.columns)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 预处理数据\n", "print(\"\\n🔄 2. 预处理数据...\")\n", "train_transactions = preprocess_transactions(train_data)\n", "test_transactions = preprocess_transactions(test_data)\n", "\n", "# 创建用于协同过滤的交易矩阵\n", "transaction_matrix = create_transaction_matrix(train_data)\n", "\n", "# 获取商品和会员统计信息\n", "print(\"\\n📈 3. 分析数据...\")\n", "item_stats = get_item_stats(train_data)\n", "member_stats = get_member_stats(train_data)\n", "\n", "print(f\"\\n🏆 前10个最常购买的商品:\")\n", "print(item_stats.head(10))\n", "\n", "print(f\"\\n👥 会员统计信息:\")\n", "print(f\"   - 每位会员平均购买商品数: {member_stats['item_count'].mean():.2f}\")\n", "print(f\"   - 每位会员平均购买不同商品数: {member_stats['unique_item_count'].mean():.2f}\")\n", "print(f\"   - 最活跃会员购买商品数: {member_stats['item_count'].max()}\")\n", "print(f\"   - 总会员数: {len(member_stats)}\")\n", "print(f\"   - 总商品种类数: {len(item_stats)}\")\n", "\n", "# 可视化商品频率\n", "plot_item_frequency(item_stats)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. 频繁模式挖掘"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n⛏️ 4. 挖掘频繁模式...\")\n", "\n", "# 设置参数\n", "min_support = 0.01  # 1% 最小支持度\n", "min_confidence = 0.5  # 50% 最小置信度\n", "\n", "print(f\"\\n🔧 算法参数:\")\n", "print(f\"   - 最小支持度: {min_support} ({min_support*100}%)\")\n", "print(f\"   - 最小置信度: {min_confidence} ({min_confidence*100}%)\")\n", "\n", "# 初始化算法\n", "apriori = Apriori(min_support=min_support, min_confidence=min_confidence)\n", "fp_growth = FPGrowth(min_support=min_support, min_confidence=min_confidence)\n", "\n", "# 评估Apriori算法\n", "print(\"\\n🔍 评估Apriori算法...\")\n", "apriori_results = evaluate_pattern_mining(apriori, train_transactions)\n", "\n", "print(f\"   ⏱️ 执行时间: {apriori_results['execution_time']:.2f} 秒\")\n", "print(f\"   📊 频繁项集数量: {apriori_results['num_frequent_itemsets']}\")\n", "print(f\"   📋 关联规则数量: {apriori_results['num_rules']}\")\n", "print(f\"   📈 平均规则置信度: {apriori_results['avg_confidence']:.4f}\")\n", "print(f\"   📈 平均规则提升度: {apriori_results['avg_lift']:.4f}\")\n", "\n", "# 评估FP-Growth算法\n", "print(\"\\n🌳 评估FP-Growth算法...\")\n", "fp_growth_results = evaluate_pattern_mining(fp_growth, train_transactions)\n", "\n", "print(f\"   ⏱️ 执行时间: {fp_growth_results['execution_time']:.2f} 秒\")\n", "print(f\"   📊 频繁项集数量: {fp_growth_results['num_frequent_itemsets']}\")\n", "print(f\"   📋 关联规则数量: {fp_growth_results['num_rules']}\")\n", "print(f\"   📈 平均规则置信度: {fp_growth_results['avg_confidence']:.4f}\")\n", "print(f\"   📈 平均规则提升度: {fp_growth_results['avg_lift']:.4f}\")\n", "\n", "# 比较算法\n", "print(\"\\n⚖️ 算法比较:\")\n", "print(f\"   - Apriori执行时间: {apriori_results['execution_time']:.2f} 秒\")\n", "print(f\"   - FP-Growth执行时间: {fp_growth_results['execution_time']:.2f} 秒\")\n", "if fp_growth_results['execution_time'] > 0:\n", "    speedup = apriori_results['execution_time'] / fp_growth_results['execution_time']\n", "    print(f\"   - FP-Growth速度提升: {speedup:.2f}倍\")\n", "else:\n", "    print(f\"   - FP-Growth速度极快！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 可视化频繁模式挖掘结果\n", "print(\"\\n📊 可视化频繁模式挖掘结果...\")\n", "\n", "# 使用更快的算法进行后续分析\n", "if fp_growth_results['execution_time'] < apriori_results['execution_time']:\n", "    selected_algorithm = fp_growth\n", "    selected_results = fp_growth_results\n", "    algorithm_name = \"FP-Growth\"\n", "else:\n", "    selected_algorithm = apriori\n", "    selected_results = apriori_results\n", "    algorithm_name = \"<PERSON><PERSON><PERSON>\"\n", "\n", "print(f\"✅ 选择 {algorithm_name} 算法进行后续分析\")\n", "\n", "# 绘制项集大小分布\n", "plot_itemset_sizes(selected_results['itemset_sizes'])\n", "\n", "# 绘制规则指标分布\n", "if selected_algorithm.association_rules:\n", "    plot_rule_metrics(selected_algorithm.association_rules)\n", "else:\n", "    print(\"⚠️ 没有生成关联规则，跳过规则指标可视化\")\n", "\n", "# 绘制执行时间比较\n", "plot_execution_time_comparison(apriori_results['execution_time'], fp_growth_results['execution_time'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 显示一些有趣的关联规则\n", "if selected_algorithm.association_rules:\n", "    print(f\"\\n🔍 发现的有趣关联规则 (前10条):\")\n", "    print(\"=\" * 80)\n", "    \n", "    for i, rule in enumerate(selected_algorithm.association_rules[:10], 1):\n", "        antecedent = \", \".join(rule['antecedent'])\n", "        consequent = \", \".join(rule['consequent'])\n", "        print(f\"{i:2d}. {antecedent} → {consequent}\")\n", "        print(f\"    置信度: {rule['confidence']:.3f}, 支持度: {rule['support']:.3f}, 提升度: {rule['lift']:.3f}\")\n", "        print()\n", "else:\n", "    print(\"⚠️ 没有生成关联规则\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. 构建推荐系统"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n🤖 5. 构建推荐系统...\")\n", "\n", "# 初始化推荐器\n", "rule_recommender = AssociationRuleRecommender(min_confidence=min_confidence)\n", "cf_recommender = CollaborativeFilteringRecommender(similarity_threshold=0.2)\n", "hybrid_recommender = HybridRecommender(rule_weight=0.7)\n", "\n", "# 训练推荐器\n", "print(\"\\n🔧 训练关联规则推荐器...\")\n", "rule_recommender.fit(selected_algorithm)\n", "\n", "print(\"🔧 训练协同过滤推荐器...\")\n", "cf_recommender.fit(transaction_matrix)\n", "\n", "print(\"🔧 训练混合推荐器...\")\n", "hybrid_recommender.fit(selected_algorithm, transaction_matrix)\n", "\n", "print(\"✅ 所有推荐器训练完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. 评估推荐系统"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n📊 6. 评估推荐系统...\")\n", "\n", "recommenders = {\n", "    '关联规则': rule_recommender,\n", "    '协同过滤': cf_recommender,\n", "    '混合推荐': hybrid_recommender\n", "}\n", "\n", "# 比较推荐器\n", "comparison_results = compare_recommenders(recommenders, test_transactions, k=5)\n", "print(\"\\n📈 推荐系统性能比较:\")\n", "print(comparison_results)\n", "\n", "# 可视化推荐器比较\n", "print(\"\\n📊 可视化推荐系统比较...\")\n", "plot_recommender_comparison(comparison_results)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 11. 示例推荐"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n🛍️ 7. 示例推荐...\")\n", "\n", "# 选择一个随机的测试交易作为示例\n", "np.random.seed(42)\n", "example_idx = np.random.randint(0, len(test_transactions))\n", "example_transaction = test_transactions[example_idx]\n", "\n", "print(f\"\\n🛒 示例购物篮: {example_transaction}\")\n", "print(\"=\" * 80)\n", "\n", "# 使用每个推荐器生成推荐\n", "for name, recommender in recommenders.items():\n", "    print(f\"\\n🎯 {name}推荐结果:\")\n", "    recommendations = recommender.recommend(example_transaction, top_n=5)\n", "    \n", "    if recommendations:\n", "        for i, rec in enumerate(recommendations, 1):\n", "            if name == '关联规则':\n", "                print(f\"  {i}. {rec['item']} (置信度: {rec['confidence']:.4f}, 提升度: {rec['lift']:.4f})\")\n", "            elif name == '协同过滤':\n", "                print(f\"  {i}. {rec['item']} (相似度: {rec['similarity']:.4f})\")\n", "            else:  # 混合推荐\n", "                print(f\"  {i}. {rec['item']} (得分: {rec['score']:.4f})\")\n", "    else:\n", "        print(\"  暂无推荐\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 12. 交互式推荐演示"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def interactive_recommendation(recommenders, item_stats):\n", "    \"\"\"\n", "    交互式推荐演示函数\n", "    \n", "    参数:\n", "        recommenders: 推荐器字典\n", "        item_stats: 商品统计信息\n", "    \"\"\"\n", "    print(\"\\n🎮 交互式推荐演示\")\n", "    print(\"=\" * 50)\n", "    print(\"您可以输入一些商品名称，系统将为您推荐相关商品\")\n", "    print(\"\\n💡 提示：以下是一些常见商品供参考:\")\n", "    \n", "    # 显示前20个最常见的商品\n", "    common_items = item_stats.head(20)['item'].tolist()\n", "    for i, item in enumerate(common_items[:10], 1):\n", "        print(f\"  {i:2d}. {item}\")\n", "    \n", "    print(\"\\n📝 请输入您的购物篮商品（用逗号分隔，例如：whole milk,yogurt）:\")\n", "    print(\"   或者直接按回车使用示例购物篮\")\n", "    \n", "    # 这里在Jupyter notebook中可以使用input()，但为了演示，我们使用预设的购物篮\n", "    # user_input = input(\">>> \")\n", "    \n", "    # 使用预设的示例购物篮\n", "    example_baskets = [\n", "        ['whole milk', 'yogurt'],\n", "        ['bread', 'butter'],\n", "        ['beer', 'chips'],\n", "        ['coffee', 'sugar'],\n", "        ['apples', 'bananas']\n", "    ]\n", "    \n", "    for basket in example_baskets:\n", "        print(f\"\\n🛒 购物篮: {', '.join(basket)}\")\n", "        print(\"-\" * 50)\n", "        \n", "        for name, recommender in recommenders.items():\n", "            print(f\"\\n🎯 {name}推荐:\")\n", "            recommendations = recommender.recommend(basket, top_n=3)\n", "            \n", "            if recommendations:\n", "                for i, rec in enumerate(recommendations, 1):\n", "                    if name == '关联规则':\n", "                        print(f\"  {i}. {rec['item']} (置信度: {rec['confidence']:.3f})\")\n", "                    elif name == '协同过滤':\n", "                        print(f\"  {i}. {rec['item']} (相似度: {rec['similarity']:.3f})\")\n", "                    else:  # 混合推荐\n", "                        print(f\"  {i}. {rec['item']} (得分: {rec['score']:.3f})\")\n", "            else:\n", "                print(\"  暂无推荐\")\n", "\n", "# 运行交互式推荐演示\n", "interactive_recommendation(recommenders, item_stats)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 13. 总结与结论"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n\" + \"=\" * 80)\n", "print(\"📋 系统总结\")\n", "print(\"=\" * 80)\n", "\n", "print(f\"\\n📊 数据统计:\")\n", "print(f\"   - 训练交易数: {len(train_transactions)}\")\n", "print(f\"   - 测试交易数: {len(test_transactions)}\")\n", "print(f\"   - 商品种类数: {len(item_stats)}\")\n", "print(f\"   - 会员数: {len(member_stats)}\")\n", "\n", "print(f\"\\n⛏️ 频繁模式挖掘结果:\")\n", "print(f\"   - 选择算法: {algorithm_name}\")\n", "print(f\"   - 执行时间: {selected_results['execution_time']:.2f} 秒\")\n", "print(f\"   - 频繁项集数: {selected_results['num_frequent_itemsets']}\")\n", "print(f\"   - 关联规则数: {selected_results['num_rules']}\")\n", "print(f\"   - 平均置信度: {selected_results['avg_confidence']:.4f}\")\n", "print(f\"   - 平均提升度: {selected_results['avg_lift']:.4f}\")\n", "\n", "print(f\"\\n🤖 推荐系统性能:\")\n", "for _, row in comparison_results.iterrows():\n", "    print(f\"   - {row['recommender']}:\")\n", "    print(f\"     * 准确率@5: {row['precision@k']:.4f}\")\n", "    print(f\"     * 召回率@5: {row['recall@k']:.4f}\")\n", "    print(f\"     * F1值@5: {row['f1@k']:.4f}\")\n", "    print(f\"     * 命中率@5: {row['hit_rate@k']:.4f}\")\n", "\n", "# 找出最佳推荐器\n", "best_recommender = comparison_results.loc[comparison_results['f1@k'].idxmax(), 'recommender']\n", "best_f1 = comparison_results['f1@k'].max()\n", "\n", "print(f\"\\n🏆 最佳推荐器: {best_recommender} (F1值: {best_f1:.4f})\")\n", "\n", "print(f\"\\n💡 主要发现:\")\n", "print(f\"   1. FP-Growth算法比Apriori算法更高效\")\n", "print(f\"   2. 混合推荐系统通常比单一方法性能更好\")\n", "print(f\"   3. 关联规则挖掘能够发现有意义的商品关联\")\n", "print(f\"   4. 协同过滤能够发现基于相似度的推荐\")\n", "\n", "print(f\"\\n🎯 商业价值:\")\n", "print(f\"   - 可以优化商品布局，将相关商品放在一起\")\n", "print(f\"   - 可以进行个性化推荐，提高销售额\")\n", "print(f\"   - 可以进行交叉销售和向上销售\")\n", "print(f\"   - 可以优化库存管理\")\n", "\n", "print(\"\\n\" + \"=\" * 80)\n", "print(\"✅ 分析完成！感谢使用频繁模式挖掘与推荐系统！\")\n", "print(\"=\" * 80)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}