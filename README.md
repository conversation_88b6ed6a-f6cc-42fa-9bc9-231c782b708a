# 频繁模式挖掘与推荐系统

本项目实现了一个基于频繁模式挖掘和推荐系统的杂货店商品推荐系统。系统能够从交易数据中挖掘频繁项集，生成关联规则，并基于这些规则构建推荐系统。

## 项目结构

项目包含以下主要模块：

- `data_processor.py`: 数据处理模块，用于加载和预处理交易数据
- `frequent_pattern_mining.py`: 频繁模式挖掘模块，实现了Apriori和FP-Growth算法
- `recommender.py`: 推荐系统模块，实现了基于关联规则和协同过滤的推荐系统
- `evaluation.py`: 评估模块，用于评估频繁模式挖掘算法和推荐系统的性能
- `visualization.py`: 可视化模块，用于可视化结果
- `main.py`: 主脚本，演示了整个系统的使用

## 功能特点

1. **频繁模式挖掘**
   - 实现了Apriori和FP-Growth算法
   - 支持自定义最小支持度和最小置信度
   - 生成关联规则并计算支持度、置信度和提升度

2. **推荐系统**
   - 基于关联规则的推荐系统
   - 基于协同过滤的推荐系统
   - 混合推荐系统（结合关联规则和协同过滤）

3. **评估**
   - 评估频繁模式挖掘算法的性能（执行时间、频繁项集数量等）
   - 评估推荐系统的性能（准确率、召回率、F1值等）
   - 购物篮补全评估

4. **可视化**
   - 商品频率可视化
   - 频繁项集大小分布可视化
   - 关联规则指标可视化
   - 关联规则网络可视化
   - 推荐系统比较可视化
   - 购物篮补全结果可视化

## 使用方法

### 安装依赖

```bash
pip install pandas numpy matplotlib seaborn networkx scikit-learn
```

### 运行系统

```bash
python main.py
```

系统将自动加载数据、挖掘频繁模式、构建推荐系统、评估性能并生成可视化结果。

## 数据集

系统使用以下数据集：

- `Groceries data train.csv`: 训练数据集
- `Groceries data test.csv`: 测试数据集

## 算法详解

### Apriori算法

Apriori算法是一种经典的频繁项集挖掘算法，基于"频繁项集的所有子集也是频繁的"这一性质。算法步骤如下：

1. 扫描数据库，计算每个项的支持度，找出所有频繁1-项集
2. 使用频繁(k-1)-项集生成候选k-项集
3. 扫描数据库，计算候选k-项集的支持度，找出所有频繁k-项集
4. 重复步骤2和3，直到无法找到更多频繁项集

### FP-Growth算法

FP-Growth算法是一种更高效的频繁项集挖掘算法，通过构建FP树来避免生成候选项集。算法步骤如下：

1. 扫描数据库，找出所有频繁1-项集及其支持度
2. 按支持度降序排序频繁1-项集
3. 构建FP树
4. 对每个频繁项，挖掘其条件模式基和条件FP树
5. 递归挖掘条件FP树，生成所有频繁项集

### 关联规则生成

从频繁项集生成关联规则的步骤如下：

1. 对每个频繁项集L，生成其所有非空子集
2. 对每个非空子集S，生成规则"S -> (L-S)"
3. 计算规则的置信度，如果大于最小置信度，则保留该规则
4. 计算规则的提升度，用于评估规则的有效性

### 推荐系统

1. **基于关联规则的推荐系统**：根据用户购物篮中的商品，找出匹配的关联规则，推荐规则右侧的商品

2. **基于协同过滤的推荐系统**：计算商品之间的相似度，根据用户购物篮中的商品，推荐相似度高的商品

3. **混合推荐系统**：结合关联规则和协同过滤的推荐结果，生成综合推荐

## 评估指标

1. **准确率（Precision）**：推荐的商品中有多少是用户实际会购买的
2. **召回率（Recall）**：用户实际会购买的商品中有多少被推荐了
3. **F1值**：准确率和召回率的调和平均
4. **命中率（Hit Rate）**：推荐列表中是否包含用户实际会购买的商品

## 结果分析

系统会生成多种可视化结果，帮助分析：

1. 商品频率分布
2. 频繁项集大小分布
3. 关联规则指标分布
4. 关联规则网络
5. 不同推荐系统的性能比较
6. 不同购物篮大小下的推荐性能

## 扩展性

系统设计具有良好的扩展性：

1. 可以轻松添加新的频繁模式挖掘算法
2. 可以轻松添加新的推荐系统算法
3. 可以处理大规模数据集（约一百万笔交易）
4. 可以自定义参数（最小支持度、最小置信度等）

## 注意事项

1. 对于大型数据集，FP-Growth算法通常比Apriori算法更高效
2. 最小支持度和最小置信度的选择会显著影响结果
3. 混合推荐系统通常比单一推荐系统性能更好
4. 可视化结果有助于理解数据和算法性能
