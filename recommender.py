"""
Recommendation System Module

This module implements recommendation systems based on frequent patterns
and collaborative filtering.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Set, Any, Optional, Union
from collections import defaultdict
from sklearn.metrics.pairwise import cosine_similarity
from frequent_pattern_mining import FrequentPatternMiner


class AssociationRuleRecommender:
    """Recommendation system based on association rules."""
    
    def __init__(self, min_confidence: float = 0.5):
        """
        Initialize the AssociationRuleRecommender.
        
        Args:
            min_confidence: Minimum confidence threshold for recommendations
        """
        self.min_confidence = min_confidence
        self.rules = []
        self.item_to_rules = defaultdict(list)
    
    def fit(self, pattern_miner: FrequentPatternMiner) -> None:
        """
        Fit the recommender using association rules from a pattern miner.
        
        Args:
            pattern_miner: Trained FrequentPatternMiner instance
        """
        # Get association rules from the pattern miner
        self.rules = pattern_miner.generate_association_rules()
        
        # Create a mapping from items to rules for faster lookup
        for i, rule in enumerate(self.rules):
            for item in rule['antecedent']:
                self.item_to_rules[item].append(i)
    
    def recommend(self, items: List[str], top_n: int = 5) -> List[Dict[str, Any]]:
        """
        Recommend items based on a user's basket.
        
        Args:
            items: List of items in the user's basket
            top_n: Number of recommendations to return
            
        Returns:
            List of recommendations, where each recommendation is a dictionary with keys:
            - item: Recommended item
            - confidence: Confidence of the recommendation
            - support: Support of the recommendation
            - lift: Lift of the recommendation
        """
        # Find rules that match the user's basket
        matching_rules = []
        items_set = set(items)
        
        # Collect rules where the antecedent is a subset of the user's basket
        for item in items:
            for rule_idx in self.item_to_rules[item]:
                rule = self.rules[rule_idx]
                if set(rule['antecedent']).issubset(items_set):
                    matching_rules.append(rule)
        
        # If no matching rules, return empty list
        if not matching_rules:
            return []
        
        # Aggregate recommendations by item
        item_scores = defaultdict(lambda: {'confidence': 0, 'support': 0, 'lift': 0, 'count': 0})
        
        for rule in matching_rules:
            for item in rule['consequent']:
                if item not in items_set:  # Only recommend items not already in the basket
                    item_scores[item]['confidence'] += rule['confidence']
                    item_scores[item]['support'] += rule['support']
                    item_scores[item]['lift'] += rule['lift']
                    item_scores[item]['count'] += 1
        
        # Calculate average scores
        recommendations = []
        for item, scores in item_scores.items():
            count = scores['count']
            if count > 0:
                recommendations.append({
                    'item': item,
                    'confidence': scores['confidence'] / count,
                    'support': scores['support'] / count,
                    'lift': scores['lift'] / count
                })
        
        # Sort by confidence (descending) and return top N
        recommendations.sort(key=lambda x: x['confidence'], reverse=True)
        return recommendations[:top_n]


class CollaborativeFilteringRecommender:
    """Recommendation system based on collaborative filtering."""
    
    def __init__(self, similarity_threshold: float = 0.2):
        """
        Initialize the CollaborativeFilteringRecommender.
        
        Args:
            similarity_threshold: Minimum similarity threshold for recommendations
        """
        self.similarity_threshold = similarity_threshold
        self.transaction_matrix = None
        self.item_similarity = None
        self.items = None
    
    def fit(self, transaction_matrix: pd.DataFrame) -> None:
        """
        Fit the recommender using a transaction matrix.
        
        Args:
            transaction_matrix: Binary transaction matrix (members x items)
        """
        self.transaction_matrix = transaction_matrix
        self.items = transaction_matrix.columns.tolist()
        
        # Calculate item-item similarity matrix
        self.item_similarity = pd.DataFrame(
            cosine_similarity(transaction_matrix.T),
            index=self.items,
            columns=self.items
        )
        
        # Set diagonal to 0 (item similarity with itself)
        np.fill_diagonal(self.item_similarity.values, 0)
    
    def recommend(self, items: List[str], top_n: int = 5) -> List[Dict[str, Any]]:
        """
        Recommend items based on a user's basket.
        
        Args:
            items: List of items in the user's basket
            top_n: Number of recommendations to return
            
        Returns:
            List of recommendations, where each recommendation is a dictionary with keys:
            - item: Recommended item
            - similarity: Similarity score of the recommendation
        """
        # Check if items exist in the training data
        valid_items = [item for item in items if item in self.items]
        
        # If no valid items, return empty list
        if not valid_items:
            return []
        
        # Calculate similarity scores for all items
        similarity_scores = defaultdict(float)
        
        for user_item in valid_items:
            for candidate_item in self.items:
                if candidate_item not in items:  # Only recommend items not already in the basket
                    similarity = self.item_similarity.loc[user_item, candidate_item]
                    if similarity >= self.similarity_threshold:
                        similarity_scores[candidate_item] += similarity
        
        # Convert to list of recommendations
        recommendations = [
            {'item': item, 'similarity': score}
            for item, score in similarity_scores.items()
        ]
        
        # Sort by similarity (descending) and return top N
        recommendations.sort(key=lambda x: x['similarity'], reverse=True)
        return recommendations[:top_n]


class HybridRecommender:
    """Hybrid recommendation system combining association rules and collaborative filtering."""
    
    def __init__(self, rule_weight: float = 0.7):
        """
        Initialize the HybridRecommender.
        
        Args:
            rule_weight: Weight for association rule recommendations (0-1)
        """
        self.rule_weight = rule_weight
        self.rule_recommender = AssociationRuleRecommender()
        self.cf_recommender = CollaborativeFilteringRecommender()
    
    def fit(self, pattern_miner: FrequentPatternMiner, transaction_matrix: pd.DataFrame) -> None:
        """
        Fit the hybrid recommender.
        
        Args:
            pattern_miner: Trained FrequentPatternMiner instance
            transaction_matrix: Binary transaction matrix (members x items)
        """
        self.rule_recommender.fit(pattern_miner)
        self.cf_recommender.fit(transaction_matrix)
    
    def recommend(self, items: List[str], top_n: int = 5) -> List[Dict[str, Any]]:
        """
        Recommend items based on a user's basket.
        
        Args:
            items: List of items in the user's basket
            top_n: Number of recommendations to return
            
        Returns:
            List of recommendations, where each recommendation is a dictionary with keys:
            - item: Recommended item
            - score: Combined recommendation score
        """
        # Get recommendations from both recommenders
        rule_recs = self.rule_recommender.recommend(items, top_n=top_n*2)
        cf_recs = self.cf_recommender.recommend(items, top_n=top_n*2)
        
        # Normalize scores
        if rule_recs:
            max_confidence = max(rec['confidence'] for rec in rule_recs)
            for rec in rule_recs:
                rec['normalized_score'] = rec['confidence'] / max_confidence if max_confidence > 0 else 0
        
        if cf_recs:
            max_similarity = max(rec['similarity'] for rec in cf_recs)
            for rec in cf_recs:
                rec['normalized_score'] = rec['similarity'] / max_similarity if max_similarity > 0 else 0
        
        # Combine recommendations
        item_scores = {}
        
        for rec in rule_recs:
            item_scores[rec['item']] = self.rule_weight * rec['normalized_score']
        
        for rec in cf_recs:
            if rec['item'] in item_scores:
                item_scores[rec['item']] += (1 - self.rule_weight) * rec['normalized_score']
            else:
                item_scores[rec['item']] = (1 - self.rule_weight) * rec['normalized_score']
        
        # Convert to list of recommendations
        recommendations = [
            {'item': item, 'score': score}
            for item, score in item_scores.items()
        ]
        
        # Sort by score (descending) and return top N
        recommendations.sort(key=lambda x: x['score'], reverse=True)
        return recommendations[:top_n]
